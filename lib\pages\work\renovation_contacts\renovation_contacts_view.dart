import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/classes/baseAddressInfo.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/pages/work/renovation_contacts/renovation_contacts_controller.dart';
import 'package:newarc_platform/pages/work/renovation_contacts/renovation_contacts_data_source.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/firestore.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/dropdown_search.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/image-select-box.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/address_completion_google.dart';
import 'package:newarc_platform/widget/UI/tab/common_icon_button.dart';
import 'package:newarc_platform/widget/renovation_contact_popup.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../../widget/UI/select-box.dart';
import '../../../widget/UI/tab/search_text_field.dart';

class RenovationContactsView extends StatefulWidget {
  //solo robe master si devono vedere
  final responsive;
  final NewarcUser newarcUser;

  const RenovationContactsView({
    Key? key,
    required this.responsive,
    required this.newarcUser
    //this.receivedContactsPageFilters
  }) : super(key: key);

  @override
  State<RenovationContactsView> createState() => _RenovationContactsViewState();
}

class _RenovationContactsViewState extends State<RenovationContactsView> {
  final controller = Get.put<RenovationContactsController>(RenovationContactsController());
  Key? paddingKey;

  @override
  void initState() {
    controller.filters.clear();
    controller.referenceFilterController.clear();
    controller.referenceFilter = '';
    initialFetchContacts();
    fetchRenovators();

    super.initState();
  }

  fetchRenovators() async {
    if (controller.renovators.length > 0) {
      controller.loadingRenovators = false;
      return;
    }
    List<NewarcUser> _renovators = [];
    setState(() {
      controller.loadingRenovators = true;
    });

    Query<Map<String, dynamic>> collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS);

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot = await collectionSnapshotQuery.where('type', isEqualTo: 'newarc').where('role', isEqualTo: 'renovator').get();

    _renovators.add(NewarcUser.empty());

    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          NewarcUser _tmp = NewarcUser.fromDocument(element.data(), element.id);
          _renovators.add(_tmp);
          controller.searchRef.add({'value': _tmp.id, 'label': _tmp.firstName! + ' ' + _tmp.lastName!});
        } catch (e) {}
      }
    }

    setState(() {
      controller.renovators = _renovators;
      controller.loadingRenovators = false;
    });
  }

  Future<void> fetchSuggestedContactsAndAgencies(setState) async {
    if (controller.suggestedContactsSearchMap.length > 0 ) {
      controller.loadingSuggestedContactsAndAgencies = false;
      return;
    }
    List<RenovationContact> _suggestedContacts = [];
    List<Agency> _agencyList = [];
    List<Map<String, String>> _suggestedContactsSearchMap = [];

    setState(() {
      controller.loadingSuggestedContactsAndAgencies = true;
    });

    QuerySnapshot<Map<String, dynamic>> contactsSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                                                          .where('isRequestingQuotation', isEqualTo: false)
                                                          .orderBy('created', descending: true)
                                                          .get();
    QuerySnapshot<Map<String, dynamic>> agencySnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES)
                                                          .get();

    if (agencySnapshot.docs.length > 0) {
      agencySnapshot.docs.forEach((value){
        _agencyList.add(Agency.fromDocument(value.data(), value.id));
      });
      if (contactsSnapshot.docs.length > 0) {
        for (var element in contactsSnapshot.docs) {
          try {
            RenovationContact _tmp = RenovationContact.fromDocument(element.data(), element.id);
            Agency _tmpAgency = _agencyList.firstWhere((agency) => agency.id == _tmp.agencyId);
            _suggestedContacts.add(_tmp);

            RenovationContactAddress _renovationContactAddress = await getRenovationContactAddress(_tmp.addressInfo!.first );
            
            // String searchSelectBoxName = "${_tmp.addressInfo!.toShortAddress()} - ${_tmp.personInfo!.name} ${_tmp.personInfo!.surname} - ${_tmpAgency.name}";

            // To be worked: Resolved
            String searchSelectBoxName = "${_renovationContactAddress.addressInfo!.toShortAddress()} - ${_tmp.personInfo!.name} ${_tmp.personInfo!.surname} - ${_tmpAgency.name}";
            
            _suggestedContactsSearchMap.add({'id': _tmp.id!, 'name': searchSelectBoxName});
          } catch (e) {}
        }
      }
    }

    setState(() {
      controller.suggestedContactsSearchMap = _suggestedContactsSearchMap;
      controller.loadingSuggestedContactsAndAgencies = false;
      controller.suggestedContactsFetchedOnce = true;
    });
  }

  Widget dataTablePagination() {
    return Visibility(
      visible: controller.currentlyShowing.isNotEmpty,
      child: Padding(
        key: paddingKey,
        padding: EdgeInsets.symmetric(vertical: 10),
        child: IconTheme.merge(
          data: const IconThemeData(opacity: 0.54),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${controller.currentlyShowing} di  ${controller.totalRecords.toString()}",
                style: TextStyle(
                  fontFamily: '',
                  fontSize: 12.0,
                  color: Colors.black.withOpacity(0.54),
                ),
              ),
              SizedBox(width: 32.0),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  if (controller.disablePreviousButton == true) return;
                  if (controller.loadingContacts == true) return;

                  fetchPrevContacts();
                },
                padding: EdgeInsets.zero,
              ),
              SizedBox(width: 24.0),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                padding: EdgeInsets.zero,
                onPressed: () {
                  if (controller.disableNextButton == true) return;
                  if (controller.loadingContacts == true) return;
                  fetchNextContacts();
                },
              ),
              SizedBox(width: 14.0),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> initialFetchContacts({bool force = false,bool reloadAll = false}) async {
    if (controller.contacts.isNotEmpty && !force && !reloadAll) return;

    controller.pageCounter = 1;

    setState(() {
      controller.contacts = [];
      controller.loadingContacts = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshotCounter;
      Query<Map<String, dynamic>> collectionSnapshotCounterQuery;

      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                                                          .where('isRequestingQuotation', isEqualTo: true)
                                                          .orderBy('created', descending: true);

      collectionSnapshotCounterQuery = FirebaseFirestore.instance
                                                        .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                                                        .where('isRequestingQuotation', isEqualTo: true)
                                                        .orderBy('created', descending: true);
      if( widget.newarcUser.isFilterPerAccountEnabled == true ) {
        collectionSnapshotQuery = collectionSnapshotQuery.where('assignedRenovatorId', isEqualTo: widget.newarcUser.id);
        collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where('assignedRenovatorId', isEqualTo: widget.newarcUser.id);
      }

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          collectionSnapshotCounterQuery = collectionSnapshotCounterQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
        }
      }

      if (reloadAll) {
        collectionSnapshot = await collectionSnapshotQuery.get();
      } else {
        collectionSnapshot = await collectionSnapshotQuery.limit(controller.recordsPerPage).get();
      }
      collectionSnapshotCounter = await collectionSnapshotCounterQuery.get();

      controller.totalRecords = collectionSnapshotCounter.docs.length;

      controller.totalPages = (controller.totalRecords / controller.recordsPerPage).ceil();

      if (controller.totalRecords > controller.recordsPerPage) {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      } else {
        controller.currentlyShowing = '1-' + controller.recordsPerPage.toString();
      }

      controller.documentList = collectionSnapshot.docs;

      await generateContacts(collectionSnapshot);

      setState(() {
        controller.loadingContacts = false;
      });
    } catch (e) {
      setState(() {
        controller.loadingContacts = false;
      });
      print(e.toString());
    }
  }

  fetchNextContacts() async {
    setState(() {
      controller.loadingContacts = true;
    });

    controller.pageCounter++;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;
      int indexOfSnapshot = isRecordExists(controller.pageCounter);

      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                                                            .where('isRequestingQuotation', isEqualTo: true)
                                                            .orderBy('created', descending: true);

        if( widget.newarcUser.isFilterPerAccountEnabled == true ) {
          collectionSnapshotQuery = collectionSnapshotQuery.where('assignedRenovatorId', isEqualTo: widget.newarcUser.id);
        }

        if (controller.filters.length > 0) {
          for (var i = 0; i < controller.filters.length; i++) {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }

        collectionSnapshot = await collectionSnapshotQuery.limit(controller.recordsPerPage).startAfterDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;

      generateContacts(collectionSnapshot);
    } catch (e) {
      setState(() {
        controller.loadingContacts = false;
      });
      print(e.toString());
    }
  }

  fetchPrevContacts() async {
    setState(() {
      controller.loadingContacts = true;
    });

    controller.pageCounter--;

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      int indexOfSnapshot = isRecordExists(controller.pageCounter);
      if (indexOfSnapshot > -1) {
        collectionSnapshot = controller.cacheFirestore[indexOfSnapshot]['snapshot'];
      } else {
        collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                                                            .where('isRequestingQuotation', isEqualTo: true)
                                                            .orderBy('created', descending: true);
        
        if( widget.newarcUser.isFilterPerAccountEnabled == true ) {
          collectionSnapshotQuery = collectionSnapshotQuery.where('assignedRenovatorId', isEqualTo: widget.newarcUser.id);
        }

        for (var i = 0; i < controller.filters.length; i++) {
          collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
        }

        collectionSnapshot = await collectionSnapshotQuery.limit(controller.recordsPerPage).endBeforeDocument(controller.documentList[controller.documentList.length - 1]).get();
      }

      controller.documentList = collectionSnapshot.docs;
      generateContacts(collectionSnapshot);
    } catch (e) {
      print(e);
      setState(() {
        controller.loadingContacts = false;
      });
    }
  }

  int isRecordExists(int pageCounter) {
    String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
    return controller.cacheFirestore.indexWhere(
      (record) => record['key'] == pageCounter && record['filtersKey'] == filtersKey,
    );
  }

  generateContacts(collectionSnapshot) async {
    if (isRecordExists(controller.pageCounter) < 0) {
      String filtersKey = controller.filters.map((f) => '${f['field']}:${f['value']}').join('|');
      controller.cacheFirestore.add({
        'key': controller.pageCounter,
        'snapshot': collectionSnapshot,
        'filtersKey': filtersKey,
      });
    }

    if (controller.pageCounter == 1) {
      controller.disablePreviousButton = true;
    } else {
      controller.disablePreviousButton = false;
    }

    if (controller.pageCounter == controller.totalPages) {
      controller.disableNextButton = true;
    } else {
      controller.disableNextButton = false;
    }

    List<RenovationContact> _contacts = [];
    for (var element in collectionSnapshot.docs) {
      try {
        var _tmp = RenovationContact.fromDocument(element.data(), element.id);

        _contacts.add(_tmp);
      } catch (e) {}
    }

    _contacts.sort((a, b) => b.created!.compareTo(a.created!));

    int lastRecordNumber = controller.pageCounter * controller.recordsPerPage;
    if (_contacts.length == controller.recordsPerPage) {
      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + lastRecordNumber.toString();
    } else if (_contacts.length > 0 && _contacts.length < controller.recordsPerPage) {
      int prevLastRecordNumber = (controller.pageCounter - 1) * controller.recordsPerPage;

      controller.currentlyShowing = (lastRecordNumber - (controller.recordsPerPage - 1)).toString() + '-' + (prevLastRecordNumber + _contacts.length).toString();
    }

    setState(() {
      controller.contacts = _contacts;
      controller.displayContacts = _contacts;
      controller.loadingContacts = false;
    });
  }

  Future<void> showAddContactPopup(RenovationContact? acquiredContact) async {
    if (acquiredContact!.id != '') {
      controller.contactNameController.text = acquiredContact.personInfo?.name ?? acquiredContact.name!;
      controller.contactSurnameController.text = acquiredContact.personInfo?.surname ?? acquiredContact.surname!;
      controller.contactEmailController.text = acquiredContact.personInfo?.email ?? acquiredContact.email!;
      controller.contactPhoneController.text = acquiredContact.personInfo?.phone ?? acquiredContact.phone!;
      controller.renovatorController.text = acquiredContact.assignedRenovatorId ?? '';
      
      // controller.renoContactAddressInfo = acquiredContact?.addressInfo != null ? acquiredContact.addressInfo! : BaseAddressInfo.empty();

      // To be worked: Resolved : Not to be used anymore
      controller.renoContactAddressInfo = BaseAddressInfo.empty();
      
    } else {
      controller.contactNameController.clear();
      controller.contactSurnameController.clear();
      controller.contactEmailController.clear();
      controller.contactPhoneController.clear();
      controller.renoContactAddressInfo = BaseAddressInfo.empty();
    }

    setState(() {
      controller.formMessages.clear();
      controller.formProgressMessage = '';
    });

    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, state) {
            return Center(
              child: BaseNewarcPopup(
                  title: acquiredContact.id == '' ? "Aggiungi contatto" : "Modifica contatto",
                  buttonText: acquiredContact.id == '' ? "Aggiungi contatto" : "Modifica contatto",
                  formErrorMessage: controller.formMessages,
                  onPressed: () async {
                    setState(() {
                      controller.formMessages.clear();
                      controller.formMessages.add('Salvataggio...');
                      controller.formProgressMessage = 'Salvataggio';
                    });

                    try {
                      Map<String, dynamic> contactData = {
                        'name': controller.contactNameController.text,
                        'surname': controller.contactSurnameController.text,
                        'email': controller.contactEmailController.text,
                        'phone': controller.contactPhoneController.text,
                      };
                      Map<String, dynamic> data = {
                        // 'name': controller.contactNameController.text,
                        // 'surname': controller.contactSurnameController.text,
                        // 'email': controller.contactEmailController.text,
                        // 'phone': controller.contactPhoneController.text,
                        'created': acquiredContact.id == "" ? DateTime.now().millisecondsSinceEpoch : acquiredContact.created!,
                        // 'streetAddress': "${controller.renoContactAddressInfo.toShortAddress()}",
                        // 'city': controller.renoContactAddressInfo.city,
                        // 'addressInfo': controller.renoContactAddressInfo.toMap(),
                        'personInfo': contactData,
                        'assignedRenovatorId': controller.renovatorController.text,
                        'files': acquiredContact.files,
                        'assignedQuotation': acquiredContact.assignedQuotation,
                      };

                      
                      /*if (!controller.renoContactAddressInfo.isValidAddress()){
                        setState(() {
                          controller.formMessages.clear();
                          controller.formMessages.add('Indirizzo non valido');
                          controller.formProgressMessage = 'Error';
                        });
                        return false;
                      }*/

                      RenovationContact renovationContact = RenovationContact(data);

                      print("renovationContact.toMap() ===> ${renovationContact.toMap()}");

                      if (acquiredContact.id == '') {
                        await FirebaseFirestore.instance
                            .collection(
                            appConfig.COLLECT_RENOVATION_CONTACTS)
                            .add(renovationContact.toMap());
                      } else {
                        updateDocument(appConfig.COLLECT_RENOVATION_CONTACTS, acquiredContact.id!, data);
                      }

                      setState(() {
                        controller.formProgressMessage = 'Salvataggio';
                        controller.formMessages.clear();
                      });

                      initialFetchContacts(force: true,reloadAll: true);
                      // return false;
                      return true;
                    } catch (e, s) {
                      setState(() {
                        controller.formMessages.clear();
                        controller.formMessages.add("L'email inserita non è valida");
                        controller.formProgressMessage = 'Error';
                      });
                      print({e, s});
                      return false;
                    }
                  },
                  // To make custom popup using the bluprint of  RenovationContactPopup
                  column: Container(
                    width: 400,
                    height: 400,
                    child: ListView(
                      children: [
                        /*AddressSearchBar(
                          label: "Indirizzo Ristrutturazione", 
                          initialAddress: acquiredContact.id == "" 
                                          ? "" 
                                          : acquiredContact.addressInfo == null 
                                              ? acquiredContact.streetAddress 
                                              : acquiredContact.addressInfo?.fullAddress ?? acquiredContact.streetAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Inserisci indirizzo completo di numero civico';
                            }
                            return null;
                          },
                          onPlaceSelected: (selectedPlace){
                            debugPrint('Selected place: \n$selectedPlace');
                            BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace["place"]);
                            if (selectedAddress.isValidAddress()){
                              controller.renoContactAddressInfo = selectedAddress;
                            } else {
                              controller.renoContactAddressInfo = BaseAddressInfo.empty();
                            }
                          }
                        ),*/
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Nome",
                              validator: (value) {
                                if (value == '') {
                                  return 'Obbligatorio!';
                                }
                                return null;
                              },
                              controller: controller.contactNameController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Cognome",
                              controller: controller.contactSurnameController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "E-Mail",
                              controller: controller.contactEmailController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Telefono",
                              controller: controller.contactPhoneController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        controller.loadingRenovators
                            ? Center(
                              child: SizedBox(
                                width: 50,
                                child: CircularProgressIndicator(
                                    color: Theme.of(context).primaryColor,
                                  ),
                              ),
                            )
                            : Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  NarFormLabelWidget(
                                    label: "Architetto",
                                    textColor: Color(0xff696969),
                                    fontSize: 13,
                                    fontWeight: '500',
                                  ),
                                  SizedBox(height: 4),
                                  NarImageSelectBoxWidget(
                                    options: controller.renovators.where((e) => e.isActive == true && e.isArchived == false).map((e) {
                                      return {'value': e.id, 'label': e.firstName! + " " + e.lastName!};
                                    }).toList(),
                                    controller: controller.renovatorController,
                                    validationType: 'required',
                                    parametersValidate: 'Obbligatorio!',
                                    onChanged: (val) {
                                      controller.renovators.where((element) => element.id == controller.renovatorController.text).first;

                                      setState(() {});
                                    },
                                  ),
                                ],
                              ),
                        SizedBox(height: 10),
                        Row(
                          children: [NarFormLabelWidget(label: controller.formProgressMessage)],
                        )
                      ],
                    ),
                  )),
            );
          });
        });
  }
  

  /*Future<bool> showAddAddressPopup( RenovationContact? acquiredContact, RenovationContactAddress address, StateSetter listSetState) async {
    
    setState(() {
      controller.formMessages.clear();
      controller.formProgressMessage = '';
    });

    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, StateSetter state) {
            return Center(
              child: BaseNewarcPopup(
                  title: address.id == '' ? "Aggiungi address" : "Modifica address",
                  buttonText: address.id == '' ? "Aggiungi address" : "Modifica address",
                  formErrorMessage: controller.formMessages,
                  onPressed: () async {
                    setState(() {
                      controller.formMessages.clear();
                      controller.formMessages.add('Salvataggio...');
                      controller.formProgressMessage = 'Salvataggio';
                    });

                    try {
                      
                      Map<String, dynamic> data = {
                        'created': address.id == "" ? DateTime.now().millisecondsSinceEpoch : address.created!,
                        'addressInfo': controller.renoContactAddressInfo,
                        'isArchived': false,
                        
                      };

                      
                      /*if (!controller.renoContactAddressInfo.isValidAddress()){
                        setState(() {
                          controller.formMessages.clear();
                          controller.formMessages.add('Indirizzo non valido');
                          controller.formProgressMessage = 'Error';
                        });
                        return false;
                      }*/

                      RenovationContactAddress renovationContactAddress = RenovationContactAddress(data);

                      
                      if (address.id == '') {
                        DocumentReference<Map<String, dynamic>> addedAddress =  await FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
                            .add(renovationContactAddress.toMap());

                        acquiredContact!.addressInfo!.add(addedAddress.id);
                        
                        updateDocument(appConfig.COLLECT_RENOVATION_CONTACTS, acquiredContact.id!, acquiredContact.toMap());
                        
                        
                      } else {
                        renovationContactAddress.created = address.created;
                        renovationContactAddress.isArchived = address.isArchived;
                        await FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
                            .doc(address.id)
                            .update(renovationContactAddress.toMap());
                      }

                      

                      setState(() {
                        controller.formProgressMessage = 'Salvataggio';
                        controller.formMessages.clear();
                      });

                      listSetState((){});

                      // initialFetchContacts(force: true,reloadAll: true);
                      // return false;
                      return true;
                    } catch (e, s) {
                      setState(() {
                        controller.formMessages.clear();
                        controller.formMessages.add(e.toString());
                        controller.formProgressMessage = 'Error';
                      });
                      print({e, s});
                      return false;
                    }
                  },
                  // To make custom popup using the bluprint of  RenovationContactPopup
                  column: Container(
                    width: 400,
                    height: 400,
                    child: ListView(
                      children: [
                        AddressSearchBar(
                          label: "Indirizzo Ristrutturazione", 
                          initialAddress: address.id == "" 
                                          ? "" 
                                          : address.addressInfo == null 
                                              ? ''
                                              : address.addressInfo!.fullAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Inserisci indirizzo completo di numero civico';
                            }
                            return null;
                          },
                          onPlaceSelected: (selectedPlace){
                            debugPrint('Selected place: \n$selectedPlace');
                            BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace["place"]);
                            if (selectedAddress.isValidAddress()){
                              controller.renoContactAddressInfo = selectedAddress;
                            } else {
                              controller.renoContactAddressInfo = BaseAddressInfo.empty();
                            }
                          }
                        ),
                        SizedBox(height: 10),
                        Row(
                          children: [NarFormLabelWidget(label: controller.formProgressMessage)],
                        )
                      ],
                    ),
                  )),
            );
          });
        });

    return false;
  } 

  Future<bool> showDeleteAddressPopup( RenovationContact? acquiredContact, RenovationContactAddress address, StateSetter listSetState) async {
    
    setState(() {
      controller.formMessages.clear();
      controller.formProgressMessage = '';
    });

    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, StateSetter state) {
            return Center(
              child: BaseNewarcPopup(
                  title: "Remove address",
                  buttonText: "Remove address",
                  formErrorMessage: controller.formMessages,
                  onPressed: () async {
                    setState(() {
                      controller.formMessages.clear();
                      controller.formMessages.add('Removing...');
                      controller.formProgressMessage = 'Removing';
                    });

                    try {
                      
                      address.isArchived = true;
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
                          .doc(address.id)
                          .update(address.toMap());

                      setState(() {
                        controller.formProgressMessage = 'Salvataggio';
                        controller.formMessages.clear();
                      });

                      listSetState((){});

                      return true;
                    } catch (e, s) {
                      setState(() {
                        controller.formMessages.clear();
                        controller.formMessages.add(e.toString());
                        controller.formProgressMessage = 'Error';
                      });
                      print({e, s});
                      return false;
                    }
                  },
                  // To make custom popup using the bluprint of  RenovationContactPopup
                  column: Container(
                    width: 400,
                    height: 100,
                    child: ListView(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            NarFormLabelWidget(label: 'Are you sure?'),
                          ],
                        )
                      ],
                    ),
                  )),
            );
          });
        });

    return false;
  }*/


  

  /*Future<void> listAddressesPopup( RenovationContact? acquiredContact ) async {
    
    setState(() {
      controller.formMessages.clear();
      controller.formProgressMessage = '';
    });

    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, StateSetter listSetState) {
            return Center(
              child: BaseNewarcPopup(
                  title: "List",
                  buttonText: "Add Address",
                  // noButton: true,
                  formErrorMessage: controller.formMessages,
                  onPressed: () async {
                    
                    // return showAddAddressPopup(acquiredContact, RenovationContactAddress.empty(), listSetState); 
                    
                    // return true;
                  },
                  // To make custom popup using the bluprint of  RenovationContactPopup
                  column: Container(
                    width: 400,
                    height: 400,
                    child: ListView(
                      children: [
                        
                        Column(
                          children: acquiredContact!.addressInfo!.map(( addressId ){

                            return FutureBuilder<RenovationContactAddress>(
                              future: getAddress(addressId), 
                              builder: (context, snapshot) {

                                if( snapshot.connectionState == ConnectionState.waiting ){
                                  return Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Text('...'),
                                  );
                                }else if (snapshot.hasError) {
                                  return Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: NarFormLabelWidget(
                                      label: "Error fetching address",
                                      fontSize: 15,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                  );
                                } else if (!snapshot.hasData || snapshot.data!.id == '' ) {
                                  return Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: NarFormLabelWidget(
                                      label: "No address found.",
                                      fontSize: 15,
                                      fontWeight: '700',
                                      textColor: Colors.black,
                                    ),
                                  );
                                }

                                RenovationContactAddress address = snapshot.data!;

                                if( address.isArchived == true ) return SizedBox(height: 0,);

                                return Card(
                                  color: Colors.white,
                                  shadowColor: Colors.black,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 5),

                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        NarFormLabelWidget(label:  address.addressInfo!.fullAddress ),
                                        Row(
                                          children: [
                                            IconButtonWidget(
                                              onTap: () {
                                                showAddAddressPopup(acquiredContact, address, listSetState);
                                              },
                                              isSvgIcon: true,
                                              icon: 'assets/icons/edit.svg',
                                              iconColor: AppColor.greyColor,
                                            ),
                                            SizedBox(width: 5,),
                                            IconButtonWidget(
                                              onTap: () {
                                                showDeleteAddressPopup(acquiredContact, address, listSetState);
                                              },
                                              isSvgIcon: true,
                                              icon: 'assets/icons/delete.svg',
                                              iconColor: AppColor.greyColor,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }
                            );
                            
                          }).toList()
                          
                        ),
                        
                      ],
                    ),
                  )),
            );
          });
        });
  }*/
  
  

  showCreateSuggestedContactPopup() {
    controller.formMessages.clear();
    controller.suggestedContactController.clear();
    controller.suggestedContactIDController.clear();
    controller.suggestedContactsSearchMap.clear();
    controller.suggestedContactsFetchedOnce = false;
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter dialogSetState) {
            if (!controller.suggestedContactsFetchedOnce){
              fetchSuggestedContactsAndAgencies(dialogSetState);
            }
            return Center(
              child: BaseNewarcPopup(
                title: "Crea da segnalazione",
                noButton: controller.loadingSuggestedContactsAndAgencies ? true : false,
                buttonText: "Crea",
                buttonColor: Theme.of(context).primaryColor,
                formErrorMessage: controller.formMessages,
                onPressed: () async {
                  controller.formMessages.clear();
                  if (controller.suggestedContactIDController.text.isEmpty) {
                    controller.formMessages.add('Seleziona una segnalazione!');
                    return false;
                  }

                  try {
                    await FirebaseFirestore.instance
                      .collection(appConfig.COLLECT_RENOVATION_CONTACTS)
                      .doc(controller.suggestedContactIDController.text)
                      .update({
                        'isRequestingQuotation': true
                      });
                    initialFetchContacts(force: true);
                    return true;
                  } catch (e) {
                    controller.formMessages.add('Errore durante l\'aggiornamento della segnalazione');
                    return false;
                  }
                },
                column: Container(
                  width: 350,
                  height: 100,
                  // padding: EdgeInsets.symmetric(vertical: 15),
                  child: controller.loadingSuggestedContactsAndAgencies
                  ? Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColor,))
                  : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: 'Cerca segnalazione',
                        textColor: Color(0xff696969),
                        fontSize: 13,
                        fontWeight: '600',
                        textAlign: TextAlign.left,
                      ),
                      SizedBox(height: 5,),
                      SearchSelectBox(
                        controller: controller.suggestedContactController,
                        options: controller.suggestedContactsSearchMap,
                        onSelection: (value){
                          controller.formMessages.clear();
                          controller.suggestedContactIDController.text = value;
                          dialogSetState(() {});
                        },
                      ),
                    ],
                  )
                )
              )
            );
          }
        );
      }
    );
  }

  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }

    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: 'Clienti ristrutturazione',
                fontSize: 19,
                fontWeight: '700',
                textColor: Colors.black,
              ),
              Expanded(
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () {
                          showCreateSuggestedContactPopup();
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColor.borderColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Crea da segnalazione",
                              style: TextStyle(
                                color: AppColor.drawerButtonColor,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10,),
                    MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: GestureDetector(
                        onTap: () async {
                          showAddContactPopup(RenovationContact.empty());
                        },
                        child: Container(
                          height: 32,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Text(
                              "Crea cliente",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
          FutureBuilder<NarFilter>(
            future: _filter(), 
            builder: (context, snapshot) {

              /* We don't need to show any special operation status */
              if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData ){
                return SizedBox(height: 0,);
              } 

              return snapshot.data!;
              
              
            }
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: AppColor.white,
              border: Border.all(width: 1.5, color: AppColor.borderColor),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loadingContacts ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          hidePaginator: true,
                          onPageChanged: (val) {
                            print("page : $val");
                          },
                          columns: [
                            DataColumn2(
                              label: Text(
                                'Nome e cognome',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Indirizzo ristrutturazione',
                              ),
                              size: ColumnSize.L
                            ),
                            DataColumn2(
                              label: Text(
                                'Architetto',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Email',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Telefono',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Inserimento',
                              ),
                              size: ColumnSize.M
                            ),
                            DataColumn2(
                              label: Text(
                                'Modifica',
                              ),
                              size: ColumnSize.S
                            ),
                          ],
                          isHasDecoration: false,
                          source: RenovationContactsDataSource(
                            onEditTap: (contact) {
                              showAddContactPopup(contact);
                            },
                            // listAddressesPopup: (contact) async {
                            //   listAddressesPopup(contact);
                            // },
                            progressMessage: controller.progressMessage,
                            renovators: controller.renovators,
                            renoFiles: controller.renoFiles,
                            displayContacts: controller.displayContacts,
                            context: context,
                          ),
                        ),
                      ),
                      if (controller.loadingContacts)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Expanded(
                //   child: DataTable2(
                //     dataRowHeight: loadingContacts ? 300 : 70,
                //     isHorizontalScrollBarVisible: true,
                //     minWidth: 1500,
                //     columnSpacing: 5,
                //     columns: getColumns(pageWidth),
                //     empty: Text('Nessun record trovato!'),
                //     rows: List.generate(displayContacts.where(filterFunction).length, (int index) {
                //       return DataRow(
                //         color: MaterialStateProperty.resolveWith((states) {
                //           return Colors.white;
                //         }),
                //         cells: getDataRow(
                //           displayContacts.where(filterFunction).elementAt(index),
                //         ),
                //       );
                //     }),
                //   ),
                // ),
                Align(
                  alignment: Alignment.centerRight,
                  child: dataTablePagination(),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }

  Future<NarFilter> _filter() async {
    return NarFilter(
      showSearchInput: true,
      searchTextEditingControllers: controller.searchTextController,
      onChanged: (String? searchQuery) async {
        if (searchQuery?.isNotEmpty ?? false) {
          List<RenovationContact> filtered = [];

          for (var contact in controller.contacts) {
            bool foundAddressFlag = false;

            if (contact.addressInfo != null && contact.addressInfo!.isNotEmpty) {
              for (var id in contact.addressInfo!) {
                DocumentSnapshot<Map<String, dynamic>> renovAddress =
                    await FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
                        .doc(id)
                        .get();

                if (renovAddress.exists) {
                  RenovationContactAddress _rca = RenovationContactAddress.fromDocument(renovAddress.data()!, renovAddress.id);

                  // To be worked: Resolved

                  final city = _rca.addressInfo?.city?.toLowerCase() ?? '';
                  final streetName = _rca.addressInfo?.streetName?.toLowerCase() ?? '';
                  final fullAddress = _rca.addressInfo?.fullAddress?.toLowerCase() ?? '';

                  if (city.contains(searchQuery!.toLowerCase()) ||
                      streetName.contains(searchQuery.toLowerCase()) ||
                      fullAddress.contains(searchQuery.toLowerCase())) {
                    foundAddressFlag = true;
                    break;
                  }
                }
              }
            }

            final name = "${contact.personInfo?.name?.toLowerCase() ?? contact.name?.toLowerCase() ?? ""} "
                "${contact.personInfo?.surname?.toLowerCase() ?? contact.surname?.toLowerCase() ?? ""}";

            if (name.contains(searchQuery!.toLowerCase()) || foundAddressFlag) {
              filtered.add(contact);
            }
          }

          setState(() {
            controller.displayContacts = filtered;
          });
        } else {
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayContacts = controller.contacts;
          });
        }
      },
      suffixIconOnTap: ()async{
        await initialFetchContacts(force: true,reloadAll: true);
        if(controller.searchTextController.text.trim().isNotEmpty){
          /* List<RenovationContact> filtered = controller.contacts.where((contact) {
            final address = contact.addressInfo;
            /* final city = address?.city?.toLowerCase() ?? contact.city?.toLowerCase() ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase() ?? contact.streetAddress?.toLowerCase() ?? ""; */
            final name = "${contact.personInfo?.name?.toLowerCase() ?? contact.name?.toLowerCase() ?? ""} ${contact.personInfo?.surname?.toLowerCase() ?? contact.surname?.toLowerCase() ?? ""}";
            /* return name.contains(controller.searchTextController.text.toLowerCase()) ||
                city.contains(controller.searchTextController.text.toLowerCase()) ||
                streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                fullAddress.contains(controller.searchTextController.text.toLowerCase()); */
            // To be worked: Resolved
            return name.contains(controller.searchTextController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.displayContacts = filtered;
          }); */
          List<RenovationContact> filtered = [];

          for (var contact in controller.contacts) {
            bool foundAddressFlag = false;

            if (contact.addressInfo != null && contact.addressInfo!.isNotEmpty) {
              for (var id in contact.addressInfo!) {
                DocumentSnapshot<Map<String, dynamic>> renovAddress =
                    await FirebaseFirestore.instance
                        .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
                        .doc(id)
                        .get();

                if (renovAddress.exists) {
                  RenovationContactAddress _rca = RenovationContactAddress.fromDocument(renovAddress.data()!, renovAddress.id);

                  // To be worked: Resolved

                  final city = _rca.addressInfo?.city?.toLowerCase() ?? '';
                  final streetName = _rca.addressInfo?.streetName?.toLowerCase() ?? '';
                  final fullAddress = _rca.addressInfo?.fullAddress?.toLowerCase() ?? '';

                  if (city.contains(controller.searchTextController.text!.toLowerCase()) ||
                      streetName.contains(controller.searchTextController.text.toLowerCase()) ||
                      fullAddress.contains(controller.searchTextController.text.toLowerCase())) {
                    foundAddressFlag = true;
                    break;
                  }
                }
              }
            }

            final name = "${contact.personInfo?.name?.toLowerCase() ?? contact.name?.toLowerCase() ?? ""} "
                "${contact.personInfo?.surname?.toLowerCase() ?? contact.surname?.toLowerCase() ?? ""}";

            if (name.contains(controller.searchTextController.text.toLowerCase()) || foundAddressFlag) {
              filtered.add(contact);
            }
          }

          setState(() {
            controller.displayContacts = filtered;
          });
        }else{
          await initialFetchContacts(force: true);
          setState(() {
            controller.displayContacts = controller.contacts;
          });
        }
      },
      textEditingControllers: [
        controller.referenceFilterController,
      ],
      selectedFilters: [
        controller.referenceFilter,
      ],
      filterFields: [
        {
          'Architetto': NarImageSelectBoxWidget(
            options: controller.searchRef,
            onChanged: (dynamic val) {
              controller.referenceFilter = val['label'];
              controller.filters = [
                {
                  'field': 'assignedRenovatorId',
                  'value': controller.referenceFilterController.text,
                }
              ];
              setState(() {});
            },
            controller: controller.referenceFilterController,
          ),
        },
      ],
      onSubmit: () async {
        await initialFetchContacts(force: true);
      },
      onReset: () async {
        controller.filters.clear();
        controller.referenceFilter = '';

        await initialFetchContacts(force: true);
      },
    );
  }

  // bool filterFunction(RenovationContact contact) {
  //   // To be implemented
  //   return true;
  // }

  // Function to generate a random salt
  String generateSalt([int length = 32]) {
    final random = Random.secure();
    var values = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(values);
  }

  // Function to hash password with salt
  String hashPassword(String password, String salt) {
    var bytes = utf8.encode(password + salt);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }
}

class RenovationContactsDataSource extends DataTableSource {
  List<RenovationContact> displayContacts;
  List<NewarcUser> renovators;
  Map<String, List> renoFiles;
  List<String> progressMessage;
  BuildContext context;
  Function(RenovationContact contact) onEditTap;
  final controller = Get.put<RenovationContactsController>(RenovationContactsController());
  
  RenovationContactsDataSource({
    required this.displayContacts,
    required this.progressMessage,
    required this.renovators,
    required this.renoFiles,
    required this.context,
    required this.onEditTap
  });

  @override
  DataRow? getRow(int index) {

    if (index < displayContacts.length) {
      final contact = displayContacts[index];

      bool isExpanded = controller.expandedGroups.contains(contact.id);


      renoFiles.addAll({contact.id!: contact.files!});
      var address = contact.streetAddress!;
      if (contact.addressInfo != null) {
        // address = contact.addressInfo!.toShortAddress();
        address = "To be worked : Resolved";
      }

      int millisecondsSinceEpoch = contact.created!;
      var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
          '/' +
          DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();

      /* Set the row height based on the expansion status. */
      double rowHeight = contact.addressInfo!.length > 1 && isExpanded ? contact.addressInfo!.length * 35 : 40; 

      return DataRow2(
        specificRowHeight: rowHeight,
        cells: [
          DataCell(

            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      
                      if (isExpanded) {
                        controller.expandedGroups.remove(contact.id);
                      } else {
                        controller.expandedGroups.add(contact.id ?? "");
                      }
                
                      notifyListeners();
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(top:7.0),
                          child: Icon(isExpanded ? Icons.expand_less : Icons.expand_more, color: contact.addressInfo!.length > 1 ? Colors.black : Colors.transparent ), // We don't need to show a icon for row which has NO ADDRESS or has only ONE ADDRESS. 
                        ),
                        SizedBox(width: 5),
                        Padding(
                          padding: const EdgeInsets.only(top:12.0),
                          child: NarFormLabelWidget(
                            label: (contact.personInfo?.name ?? (contact.name ?? "")) + ' ' + (contact.personInfo?.surname ?? (contact.surname ?? "")),
                            fontSize: 12,
                            fontWeight: '600',
                            overflow: TextOverflow.ellipsis,
                            textColor: Colors.black,
                          ),
                        ),
                        SizedBox(width:10),
                        contact.isFirebaseUser ? SvgPicture.asset(
                            'assets/icons/account.svg',
                            color: AppColor.drawerIconButtonColor,
                            width: 15,
                          ) : SizedBox(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          DataCell(

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: 
              
              controller.contactAddress[contact.id] != null
              ? (controller.contactAddress[contact.id!] ?? [])
                .take(isExpanded ? controller.contactAddress[contact.id]!.length : 1 ) // This argument will controll the number of items to be displayed from the list. Because for Expanded row we need to show all the addresses and for the collapsed we just need to show 1 address. 
                .map((addressData) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: NarFormLabelWidget(
                      label: addressData,
                      fontSize: 12,
                      fontWeight: '600',
                      textAlign: TextAlign.start,
                      textColor: Colors.black,
                    ),
                  );
                }).toList()
              : contact.addressInfo!.map((addressId){
                return FutureBuilder<RenovationContactAddress>(
                  future: getRenovationContactAddress(addressId ),
                  builder: (context, snapshot) {

                    /* We don't need to show any special operation status */
                    if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData || snapshot.data!.id == '' ){
                      return SizedBox(height: 0,);
                    } 

                    /* To avoid extra calls. Push the addresses to a list 
                    So that we can avoid the calls when next time the notifyListerner is called.
                    Because there is not such operation is performed on the address
                    */
                    if (!controller.contactAddress.containsKey(contact.id!)) {
                      controller.contactAddress[contact.id!] = [];
                    }

                    controller.contactAddress[contact.id!]!.add(
                      snapshot.data!.addressInfo!.toShortAddress()
                    );
                    
                    /* show a palceholder widget if the address index is greater than 0 
                      otherwise is show a Overflow UI error.
                    */

                    if( contact.addressInfo!.indexOf(addressId) > 0 ) {
                      return SizedBox(height: 0,);
                    }
                    
                    /* Display only first address when it's not expanded.
                    */
                    return Padding(
                      padding: const EdgeInsets.symmetric( vertical: 8.0),
                      child: NarFormLabelWidget(
                        label: snapshot.data!.addressInfo!.toShortAddress(),
                        fontSize: 12,
                        fontWeight: '600',
                        textAlign: TextAlign.start,
                        textColor: Colors.black,
                      ),
                    );
                  }
                );
              }).toList(),
            )
            
            
          ),
          DataCell(
            Container(
              child: contact.assignedRenovatorId == null || contact.assignedRenovatorId == "" || !renovators.map((elem) => elem.id).toList().contains(contact.assignedRenovatorId)
                  ? Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                        color: Colors.grey,
                      ),
                    )
                  : FutureBuilder<ImageProvider>(
                      future: getImage(contact.assignedRenovatorId!),
                      builder: (context, snapshot) {
                        NewarcUser _user = renovators.where((element) => element.id == contact.assignedRenovatorId).first;
                        if (snapshot.connectionState == ConnectionState.waiting){
                          return Container(
                            height: 15,
                            width: 15,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Theme.of(context).primaryColor,
                            ),
                          );
                        } else if (snapshot.hasData) {
                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              ClipOval(
                                child: Image(
                                  image: snapshot.data!,
                                  width: 30,
                                  height: 30,

                                  fit: BoxFit.cover, // Adjust as needed
                                ),
                              ),
                              SizedBox(width: 10),
                              Flexible(
                                child: NarFormLabelWidget(
                                  label: _user.firstName! + ' ' + _user.lastName!,
                                  fontSize: 12,
                                  fontWeight: '600',
                                  overflow: TextOverflow.ellipsis,
                                  textColor: Colors.black,
                                ),
                              ),
                            ],
                          );
                        } else if (snapshot.hasError) {
                          return NarFormLabelWidget(
                                label: _user.firstName! + ' ' + _user.lastName!,
                                fontSize: 12,
                                fontWeight: '600',
                                overflow: TextOverflow.ellipsis,
                                textColor: Colors.black,
                              );
                        }
                        return SizedBox();
                      },
                    ),
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: contact.personInfo?.email ?? (contact.email ?? "NoEmail"),
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: contact.personInfo?.phone ?? (contact.phone ?? "NoEmail"),
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          DataCell(
            NarFormLabelWidget(
              label: date,
              fontSize: 12,
              fontWeight: '600',
              overflow: TextOverflow.ellipsis,
              textColor: Colors.black,
            ),
          ),
          // DataCell(
          //   IconButtonWidget(
          //     onTap: () {
          //       showContactPopup(contact);
          //     },
          //     isSvgIcon: true,
          //     icon: 'assets/icons/account.svg',
          //     iconColor: AppColor.greyColor,
          //   ),
          // ),
          // DataCell(
          //   Row(
          //     // mainAxisAlignment: MainAxisAlignment.start,
          //     crossAxisAlignment: CrossAxisAlignment.center,
          //     // mainAxisSize: MainAxisSize.max,
          //     children: [
          //       NarFilePickerWidget(
          //         allowMultiple: false,
          //         displayFormat: 'inline-button',
          //         borderRadius: 7,
          //         fontSize: 12,
          //         fontWeight: '600',
          //         text: 'Carica',
          //         height: 15,
          //         borderSideColor: Theme.of(context).primaryColor,
          //         hoverColor: Color.fromRGBO(133, 133, 133, 1),
          //         allFiles: renoFiles[contact.id],
          //         pageContext: context,
          //         storageDirectory: 'renovation/${contact.id}/',
          //         progressMessage: progressMessage,
          //         removeExistingOnChange: true,
          //         displayButtonAsLink: true,

          //         // displayInlineWidget: displayInlineWidget,
          //         onUploadCompleted: () async {
          //           final FirebaseFirestore _db = FirebaseFirestore.instance;
          //           contact.files = renoFiles[contact.id];

          //           try {
          //             await _db.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(contact.id).update(contact.toMap());

          //             // print({'saved', widget.allFiles});

          //             // if (mounted) {
          //             //   setState(() {
          //             progressMessage.clear();
          //             progressMessage.add('');
          //             // });
          //             // }
          //           } catch (e) {
          //             // if (mounted) {
          //             //   setState(() {
          //             progressMessage.clear();
          //             progressMessage.add('Error');
          //             // });
          //             // }
          //           }
          //         },
          //       ),
          //       NarFilePickerWidget(
          //         allowMultiple: false,
          //         filesToDisplayInList: 0,
          //         removeButton: false,
          //         isDownloadable: false,
          //         removeButtonText: 'Elimina',
          //         uploadButtonPosition: 'back',
          //         showMoreButtonText: '+ espandi',
          //         actionButtonPosition: 'bottom',
          //         displayFormat: 'inline-widget',
          //         containerWidth: 45,
          //         containerHeight: 45,
          //         containerBorderRadius: 13,
          //         borderRadius: 7,
          //         fontSize: 11,
          //         fontWeight: '600',
          //         text: 'Carica Progetto',
          //         borderSideColor: Theme.of(context).primaryColor,
          //         hoverColor: Color.fromRGBO(133, 133, 133, 1),
          //         allFiles: renoFiles[contact.id],
          //         pageContext: context,
          //         storageDirectory: 'renovation/${contact.id}/',
          //         removeExistingOnChange: true,
          //         progressMessage: progressMessage,
          //         showTitle: false,
          //         thumbnailIcon: IconButtonWidget(
          //           onTap: () {
          //             showContactPopup(contact);
          //           },
          //           isSvgIcon: false,
          //           icon: 'assets/icons/document.png',
          //           iconColor: AppColor.greyColor,
          //         ),
          //         // thumbnailIcon: Container(
          //         //   child: Image.asset('assets/icons/document.png', height: 20),
          //         //   padding: const EdgeInsets.all(6),
          //         //   decoration: BoxDecoration(
          //         //     color: Color(0xffF2F2F2),
          //         //     borderRadius: BorderRadius.circular(7.0),
          //         //   ),
          //         // ),
          //         onUploadCompleted: () async {
          //           final FirebaseFirestore _db = FirebaseFirestore.instance;
          //           contact.files = renoFiles[contact.id];

          //           try {
          //             await _db.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(contact.id).update(contact.toMap());

          //             // print({'saved', widget.allFiles});

          //             // if (mounted) {
          //             //   setState(() {
          //             progressMessage.clear();
          //             progressMessage.add('');
          //             // });
          //             // }
          //           } catch (e) {
          //             // if (mounted) {
          //             //   setState(() {
          //             progressMessage.clear();
          //             progressMessage.add('Error');
          //             // });
          //             // }
          //           }
          //         },
          //       )
          //     ],
          //   ),
          // ),
          // DataCell(
          //   contact.renovationStatus == null
          //       ? SizedBox()
          //       : StatusWidget(
          //           status: contact.renovationStatus == 'in-corso' ? "In corso" : "Completato",
          //           statusColor: contact.renovationStatus == 'in-corso' ? Color(0xfff5c620) : Theme.of(context).primaryColor,
          //         ),
          // ),
          DataCell(
            contact.isFirebaseUser
                ? SizedBox()
                :
            IconButtonWidget(
              onTap: () {
                onEditTap(contact);
              },
              isSvgIcon: true,
              icon: 'assets/icons/edit.svg',
              iconColor: AppColor.greyColor,
            ),
          ),
        ],
      );
    }

    return null;
  }

  Future<ImageProvider> getImage(String userId) async {
    final extensions = ['.jpeg', '.png', '.jpg'];
    for (final extension in extensions) {
      final ref = FirebaseStorage.instance.ref().child('users/$userId/profile$extension');
      try {
        final url = await ref.getDownloadURL();
        return NetworkImage(url);
      } catch (error) {
        continue;
      }
    }
    throw Exception('Profile image not found for user $userId');
  }


  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => displayContacts.length;

  @override
  int get selectedRowCount => 0;
}
// List<DataCell> getDataRow(RenovationContact contact) {
//   List<DataCell> list = [];
//
//   // print({'contacts', contact.toMap()});
//
//   renoFiles.addAll({contact.id!: contact.files!});
//
//   var address = contact.streetAddress!;
//   list.add(
//     DataCell(
//       Stack(
//         children: [
//           Padding(
//             padding: const EdgeInsets.only(left: 10),
//             child: NarFormLabelWidget(
//               label: address,
//               textAlign: TextAlign.start,
//               textColor: Colors.black,
//               fontWeight: '500',
//               fontSize: 14,
//               overflow: TextOverflow.ellipsis,
//             ),
//           )
//         ],
//       ),
//     ),
//   );
//
//   int millisecondsSinceEpoch = contact.created!;
//
//   // 2. Data
//   //2 Data
//   var date = DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).day.toString() +
//       '/' +
//       DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).month.toString() +
//       '/' +
//       DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch).year.toString();
//
//   // Inserimento
//   list.add(
//     DataCell(Container(
//       child: NarFormLabelWidget(
//         label: date,
//         fontSize: 14,
//         fontWeight: '500',
//         textColor: Colors.black,
//       ),
//     )),
//   );
//
//   // 3. User profile
//   list.add(
//     DataCell(
//       Container(
//         // color: Colors.amber,
//         child: TextButton(
//           child: Container(
//             child: Image.asset('assets/icons/user-icon.png', height: 20),
//             padding: const EdgeInsets.all(6),
//             decoration: BoxDecoration(
//               color: Color.fromRGBO(227, 227, 227, 1),
//               borderRadius: BorderRadius.circular(7.0),
//             ),
//           ),
//           onPressed: () {
//             showContactPopup(contact);
//           },
//           style: ButtonStyle(overlayColor: MaterialStateProperty.all(Colors.transparent)),
//         ),
//       ),
//     ),
//   );
//
//   //5 Assegnazione
//   //ZLJbHfa1pTTlDpGnz4VPqiTOemr2
//   list.add(DataCell(Container(
//       child: contact.assignedRenovatorId == null
//           ? Container(
//               width: 30,
//               height: 30,
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(100),
//                 color: Colors.grey,
//               ),
//             )
//           : FutureBuilder<ImageProvider>(
//               future: getImage(contact.assignedRenovatorId!),
//               builder: (context, snapshot) {
//                 if (snapshot.hasData) {
//                   NewarcUser _user = renovators.where((element) => element.id == contact.assignedRenovatorId).first;
//                   if (_user.id == '' || snapshot.hasError) {
//                     return Container(
//                       width: 30,
//                       height: 30,
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(100),
//                         color: Colors.grey,
//                       ),
//                     );
//                   } else if (_user.profilePicture == '') {
//                     return Row(
//                       children: [
//                         Container(
//                           width: 30,
//                           height: 30,
//                           decoration: BoxDecoration(
//                             borderRadius: BorderRadius.circular(100),
//                             color: Colors.grey,
//                           ),
//                         ),
//                         SizedBox(width: 10),
//                         NarFormLabelWidget(
//                           label: _user.firstName! + ' ' + _user.lastName!,
//                           fontWeight: '500',
//                         )
//                       ],
//                     );
//                   }
//                   return Row(
//                     children: [
//                       ClipOval(
//                         child: Image(
//                           image: snapshot.data!,
//                           width: 30,
//                           height: 30,
//
//                           fit: BoxFit.cover, // Adjust as needed
//                         ),
//                       ),
//                       SizedBox(width: 10),
//                       NarFormLabelWidget(
//                         label: _user.firstName! + ' ' + _user.lastName!,
//                         fontWeight: '500',
//                       )
//                     ],
//                   );
//                 } else if (snapshot.hasError) {
//                   return Container(
//                     width: 30,
//                     height: 30,
//                     decoration: BoxDecoration(
//                       borderRadius: BorderRadius.circular(100),
//                       color: Colors.grey,
//                     ),
//                   );
//                 }
//                 // Display a loading indicator while waiting
//                 return Container(
//                   width: 30,
//                   height: 30,
//                   decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(100),
//                     color: Colors.grey,
//                   ),
//                 );
//               },
//             ))));
//
//   //5 Telefono
//   // list.add(
//   //   DataCell(CustomDropdown2(
//   //     isMaster: true,
//   //     renovationContact: contact,
//   //     updateStage: updateContactStatus,
//   //     fontSize: 13,
//   //     height: 35,
//   //     width: 150,
//   //   )),
//   // );
//
//   list.add(
//     DataCell(Row(
//       // mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.center,
//       // mainAxisSize: MainAxisSize.max,
//       children: [
//         NarFilePickerWidget(
//           allowMultiple: false,
//           displayFormat: 'inline-button',
//           borderRadius: 7,
//           fontSize: 14,
//           fontWeight: '600',
//           text: 'Carica',
//           height: 15,
//           borderSideColor: Theme.of(context).primaryColor,
//           hoverColor: Color.fromRGBO(133, 133, 133, 1),
//           allFiles: renoFiles[contact.id],
//           pageContext: context,
//           storageDirectory: 'renovation/${contact.id}/',
//           progressMessage: progressMessage,
//           removeExistingOnChange: true,
//           displayButtonAsLink: true,
//
//           // displayInlineWidget: displayInlineWidget,
//           onUploadCompleted: () async {
//             final FirebaseFirestore _db = FirebaseFirestore.instance;
//             contact.files = renoFiles[contact.id];
//
//             try {
//               await _db.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(contact.id).update(contact.toMap());
//
//               // print({'saved', widget.allFiles});
//
//               if (mounted) {
//                 setState(() {
//                   progressMessage.clear();
//                   progressMessage.add('');
//                 });
//               }
//             } catch (e) {
//               if (mounted) {
//                 setState(() {
//                   progressMessage.clear();
//                   progressMessage.add('Error');
//                 });
//               }
//             }
//           },
//         ),
//         NarFilePickerWidget(
//           allowMultiple: false,
//           filesToDisplayInList: 0,
//           removeButton: false,
//           isDownloadable: false,
//           removeButtonText: 'Elimina',
//           uploadButtonPosition: 'back',
//           showMoreButtonText: '+ espandi',
//           actionButtonPosition: 'bottom',
//           displayFormat: 'inline-widget',
//           containerWidth: 45,
//           containerHeight: 45,
//           containerBorderRadius: 13,
//           borderRadius: 7,
//           fontSize: 11,
//           fontWeight: '600',
//           text: 'Carica Progetto',
//           borderSideColor: Theme.of(context).primaryColor,
//           hoverColor: Color.fromRGBO(133, 133, 133, 1),
//           allFiles: renoFiles[contact.id],
//           pageContext: context,
//           storageDirectory: 'renovation/${contact.id}/',
//           removeExistingOnChange: true,
//           progressMessage: progressMessage,
//           showTitle: false,
//           thumbnailIcon: Container(
//             child: Image.asset('assets/icons/document.png', height: 20),
//             padding: const EdgeInsets.all(6),
//             decoration: BoxDecoration(
//               color: Color(0xffF2F2F2),
//               borderRadius: BorderRadius.circular(7.0),
//             ),
//           ),
//           onUploadCompleted: () async {
//             final FirebaseFirestore _db = FirebaseFirestore.instance;
//             contact.files = renoFiles[contact.id];
//
//             try {
//               await _db.collection(appConfig.COLLECT_RENOVATION_CONTACTS).doc(contact.id).update(contact.toMap());
//
//               // print({'saved', widget.allFiles});
//
//               if (mounted) {
//                 setState(() {
//                   progressMessage.clear();
//                   progressMessage.add('');
//                 });
//               }
//             } catch (e) {
//               if (mounted) {
//                 setState(() {
//                   progressMessage.clear();
//                   progressMessage.add('Error');
//                 });
//               }
//             }
//           },
//         )
//       ],
//     )
//         // CustomDropdown2(
//         //   isMaster: true,
//         //   renovationContact: contact,
//         //   updateStage: updateContactStatus,
//         //   fontSize: 13,
//         //   height: 35,
//         //   width: 150,
//         // )
//         ),
//   );
//
//   //6 Stato ristrutturazione
//
//   list.add(DataCell(contact.renovationStatus == null
//       ? Container()
//       : Container(
//           width: 120,
//           height: 35,
//           alignment: Alignment.center,
//           decoration: BoxDecoration(color: contact.renovationStatus == 'in-corso' ? Color(0xfff5c620) : Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(7)),
//           child: contact.renovationStatus == 'in-corso'
//               ? NarFormLabelWidget(
//                   label: "In corso",
//                   textColor: Colors.white,
//                   fontSize: 14,
//                   fontWeight: '500',
//                 )
//               : NarFormLabelWidget(
//                   label: "Completato",
//                   textColor: Colors.white,
//                   fontSize: 14,
//                   fontWeight: '500',
//                 ),
//         )));
//
//   // Modifica
//   list.add(
//     DataCell(
//       Container(
//         child: TextButton(
//           child: Container(
//             child: SvgPicture.asset('assets/icons/edit.svg', height: 20),
//             padding: const EdgeInsets.all(6),
//             decoration: BoxDecoration(
//               color: Color.fromRGBO(227, 227, 227, 1),
//               borderRadius: BorderRadius.circular(7.0),
//             ),
//           ),
//           onPressed: () {
//             showAddContactPopup(contact);
//           },
//           style: ButtonStyle(overlayColor: WidgetStateProperty.all(Colors.transparent)),
//         ),
//       ),
//     ),
//   );
//
//   return list;
// }

// getColor(String status) {
//   switch (status) {
//     case 'Da contattare':
//       return Color(0xff5FBCEC);
//     case 'Contattato':
//       return Color(0xffFFC633);
//     case 'Non interessato':
//       return Color(0xffFF5E53);
//     case 'Acquisito':
//       return Color(0xff489B79);
//   }
// }

// List<DropdownMenuItem> buildDropdownTestItems(List statusList) {
//   List<DropdownMenuItem> items = [];
//   for (var i in statusList) {
//     items.add(
//       DropdownMenuItem(
//         value: i,
//         child: Container(
//           decoration: BoxDecoration(
//             color: getColor(i['keyword']),
//             borderRadius: BorderRadius.circular(8),
//           ),
//           padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
//           child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
//             Text(
//               i['keyword'],
//               style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
//             ),
//           ]),
//         ),
//       ),
//     );
//   }
//   return items;
// }

// onChangeDropdownTests(String selectedStatus) {
//   int i = statusList.indexWhere((element) => element['keyword'] == selectedStatus);
//
//   setState(() {
//     _selectedStatus = statusList.elementAt(i);
//   });
// }

