import 'dart:developer';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:newarc_platform/classes/renovationContact.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/widget/UI/filter.dart';
import 'package:newarc_platform/widget/UI/newarc_data_table.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/pages/work/suggested_contacts/suggested_contacts_controller.dart';
import 'package:newarc_platform/pages/work/suggested_contacts/suggested_contacts_data_source.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import '../../../classes/agencyUser.dart';
import '../../../widget/UI/select-box.dart';


class SuggestedContactsWorkView extends StatefulWidget {
  final responsive;
  final String? agencyId;

  SuggestedContactsWorkView({
    Key? key, 
    required this.responsive, 
    this.agencyId,
    }) : super(key: key);

  @override
  State<SuggestedContactsWorkView> createState() =>
      _SuggestedContactsWorkViewState();
}

class _SuggestedContactsWorkViewState extends State<SuggestedContactsWorkView> {
  
  final controller = Get.put<SuggestedContactsWorkController>(SuggestedContactsWorkController());
  Key? paddingKey;

  @override
  void initState() {
    controller.clearFilter();
    fetchAllAgencies();
    initialFetchSuggestedContacts(force: true);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> initialFetchSuggestedContacts({bool force = false}) async {

    if (controller.contacts.isNotEmpty && !force) return;

    setState(() {
      controller.contacts = [];
      controller.loading = true;
    });

    try {
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_RENOVATION_CONTACTS);

      collectionSnapshotQuery = collectionSnapshotQuery
        .where('isArchived', isEqualTo: false)
        .where('isSuggestedContact', isEqualTo: true)
        .orderBy('created', descending: true);

      if (controller.filters.length > 0) {
        for (var i = 0; i < controller.filters.length; i++) {
          if (controller.filters[i]['search'] == 'equal') {
            collectionSnapshotQuery = collectionSnapshotQuery.where(controller.filters[i]['field'], isEqualTo: controller.filters[i]['value']);
          }
        }
      }

      collectionSnapshot = await collectionSnapshotQuery.get();

      for (var i = 0; i < collectionSnapshot.docs.length; i++) {
        RenovationContact tmpSuggCont = RenovationContact.fromDocument(
            collectionSnapshot.docs[i].data(), collectionSnapshot.docs[i].id);
        controller.contacts.add(tmpSuggCont);
      }
      setState(() {
        controller.loading = false;
      });
    } catch (e, s) {
      setState(() {
        controller.loading = false;
      });
      log('Following error', error: e, stackTrace: s);
    }
  }

  Future<void> fetchAllAgencies() async {
    List<Agency> agencyList = [];

    try {
      QuerySnapshot<Map<String, dynamic>> snapshot = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCIES)
          .get();

      for (var doc in snapshot.docs) {
        if (doc.exists) {
          Agency agency = Agency.fromDocument(doc.data(), doc.id);
          agencyList.add(agency);
        }
      }
    } catch (e) {
      print('Error fetching agencies: $e');
    }

    setState(() {
      controller.agencyList = agencyList;
    });
  }

  List<Map> suggestionStatus = [
    {
      'value': 'segnalato',
      'label': 'Segnalato',
      'bgColor': Color(0xffD4D4D4),
      'textColor': Colors.black
    },
    {
      'value': 'acquisito',
      'label': 'Acquisito',
      'bgColor': Color(0xff39C14F),
      'textColor': Colors.white
    },
    {
      'value': 'non-acquisito',
      'label': 'Non acquisito',
      'bgColor': Color(0xffDD0000),
      'textColor': Colors.white
    },
  ];

  Future<NarFilter> narFilter() async {
    
    return NarFilter(
      showSearchInput: true,
      searchHintText: "Cerca per indirizzo o per nome...",
      searchTextEditingControllers: controller.searchController,
      onChanged: (String? searchQuery)async{
        if(searchQuery?.isNotEmpty ?? false){
          if (searchQuery != null && searchQuery.isNotEmpty) {
            // List<RenovationContact> filtered = controller.contacts.where((contact) {
            //   final address = contact.addressInfo;
            //   // To be worked: Resolved
            //   /* final city = address?.city?.toLowerCase() ?? contact.city?.toLowerCase() ?? "";
            //   final streetName = address?.streetName?.toLowerCase() ?? '';
            //   final fullAddress = address?.fullAddress?.toLowerCase() ?? contact.streetAddress?.toLowerCase() ?? ""; */
            //   final name = "${contact.personInfo?.name?.toLowerCase() ?? contact.name?.toLowerCase() ?? ""} ${contact.personInfo?.surname?.toLowerCase() ?? contact.surname?.toLowerCase() ?? ""}";
              
            //   /* return name.contains(searchQuery.toLowerCase()) ||
            //       city.contains(searchQuery.toLowerCase()) ||
            //       streetName.contains(searchQuery.toLowerCase()) ||
            //       fullAddress.contains(searchQuery.toLowerCase()); */
            //   return name.contains(searchQuery.toLowerCase());
            // }).toList();

            // setState(() {
            //   controller.contacts = filtered;
            // });
            List<RenovationContact> filtered = [];

            for (var contact in controller.contacts) {
              bool foundAddressFlag = false;

              if (contact.addressInfo != null && contact.addressInfo!.isNotEmpty) {
                for (var id in contact.addressInfo!) {
                  DocumentSnapshot<Map<String, dynamic>> renovAddress =
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_RENOVATION_CONTACT_ADDRESS)
                          .doc(id)
                          .get();

                  if (renovAddress.exists) {
                    RenovationContactAddress _rca = RenovationContactAddress.fromDocument(renovAddress.data()!, renovAddress.id);

                    final city = _rca.addressInfo?.city?.toLowerCase() ?? '';
                    final streetName = _rca.addressInfo?.streetName?.toLowerCase() ?? '';
                    final fullAddress = _rca.addressInfo?.fullAddress?.toLowerCase() ?? '';

                    if (city.contains(searchQuery!.toLowerCase()) ||
                        streetName.contains(searchQuery.toLowerCase()) ||
                        fullAddress.contains(searchQuery.toLowerCase())) {
                      foundAddressFlag = true;
                      break;
                    }
                  }
                }
              }

              final name = "${contact.personInfo?.name?.toLowerCase() ?? contact.name?.toLowerCase() ?? ""} "
                  "${contact.personInfo?.surname?.toLowerCase() ?? contact.surname?.toLowerCase() ?? ""}";

              if (name.contains(searchQuery!.toLowerCase()) || foundAddressFlag) {
                filtered.add(contact);
              }
            }

            setState(() {
              controller.contacts = filtered;
            });
          }
        }else{
          await initialFetchSuggestedContacts(force: true);
          setState(() {
            controller.contacts = controller.contacts;
          });
        }
      },
      suffixIconOnTap: ()async{
        await initialFetchSuggestedContacts(force: true);
        if(controller.searchController.text.trim().isNotEmpty){
          List<RenovationContact> filtered = controller.contacts.where((contact) {
            final address = contact.addressInfo;
            /* final city = address?.city?.toLowerCase() ?? contact.city?.toLowerCase() ?? "";
            final streetName = address?.streetName?.toLowerCase() ?? '';
            final fullAddress = address?.fullAddress?.toLowerCase() ?? contact.streetAddress?.toLowerCase() ?? ""; */
            final name = "${contact.personInfo?.name?.toLowerCase() ?? contact.name?.toLowerCase() ?? ""} ${contact.personInfo?.surname?.toLowerCase() ?? contact.surname?.toLowerCase() ?? ""}";
            /* return name.contains(controller.searchController.text.toLowerCase()) ||
                city.contains(controller.searchController.text.toLowerCase()) ||
                streetName.contains(controller.searchController.text.toLowerCase()) ||
                fullAddress.contains(controller.searchController.text.toLowerCase()); */

            return name.contains(controller.searchController.text.toLowerCase());
          }).toList();

          setState(() {
            controller.contacts = filtered;
          });
        }else{
          await initialFetchSuggestedContacts(force: true);
          setState(() {
            controller.contacts = controller.contacts;
          });
        }
      },
      textEditingControllers: [controller.agencyFilterController],
      selectedFilters: [controller.agencySelectedFilter],
      filterFields: [
        {
          'Agenzia': NarSelectBoxWidget(
            options: controller.agencyList
                .map((e) => e.name!)
                .toSet()
                .toList(),
            controller: controller.agencyFilterController,
            onChanged: (value) {
              Agency _selectedAgency = controller.agencyList.where((agency) => agency.name! == controller.agencyFilterController.text).first;
              controller.filters.removeWhere((element) {
                return element['field'] == 'agencyId';
              });

              controller.filters.add({'field': 'agencyId', 'value': _selectedAgency.id, 'search': 'equal'});
              controller.agencySelectedFilter = controller.agencyFilterController.text;
            },
          ),
        },
      ],
      onSubmit: () async {
        await initialFetchSuggestedContacts(force: true);
      },
      onReset: () async {
        controller.clearFilter();
        await initialFetchSuggestedContacts(force: true);
      },
    );
  }


  @override
  Widget build(BuildContext context) {
    double pageWidth = MediaQuery.of(context).size.width - 260;
    if (pageWidth < 1800) {
      pageWidth = 1800;
    }
    return LayoutBuilder(builder: (context, constraints) {
      return Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NarFormLabelWidget(
            label: 'Ristrutturazioni segnalate',
            fontSize: 19,
            fontWeight: '700',
          ),
          Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              FutureBuilder<NarFilter>(
                future: narFilter(), 
                builder: (context, snapshot) {

                  /* We don't need to show any special operation status */
                  if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData ){
                    return SizedBox(height: 0,);
                  } 

                  return snapshot.data!;
                  
                  
                }
              ),
            ],
          ),
          SizedBox(height: 10),
          Container(
            height: constraints.maxHeight / 1.2,
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Opacity(
                        opacity: controller.loading ? 0.5 : 1,
                        child: NewarcDataTable(
                          rowsPerPage: 20,
                          dividerThickness: 1,
                          columns: [
                            DataColumn2(
                              size: ColumnSize.L,
                              label: Text(
                                'Indirizzo',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Nome e Cognome',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Telefono',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Email',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Agenzia',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.S,
                              label: Text(
                                'Commissione',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Azioni',
                              ),
                            ),
                            DataColumn2(
                              size: ColumnSize.M,
                              label: Text(
                                'Data segnalazione',
                              ),
                            ),
                          ],
                          source: SuggestedContactsWorkDataSource(
                            initialFetchContacts: initialFetchSuggestedContacts,
                            contacts: controller.contacts,
                            status: suggestionStatus,
                            context: context,
                          ),
                        ),
                      ),
                      if (controller.loading)
                        Positioned.fill(
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),
                      // dataTablePagination(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
