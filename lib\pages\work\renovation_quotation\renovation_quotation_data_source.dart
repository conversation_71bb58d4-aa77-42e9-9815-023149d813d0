import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:newarc_platform/classes/renovationContactAddress.dart';
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/classes/user.dart';
import 'package:newarc_platform/pages/work/renovation_quotation/renovation_quotation_controller.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:intl/intl.dart';
import '../../../utils/common_utils.dart';
import '../../../widget/UI/image_dropdown.dart';
import '../../../widget/UI/tab/status_widget.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;

class RenovationQuotationDataSource extends DataTableSource {
  final List<List<RenovationQuotation>> groupedQuotations;
  final List<NewarcUser> renovators;
  final BuildContext context;
  final List<Map> status;
  final Function() initialFetchContacts;
  final Function(RenovationQuotation row, String type,List<RenovationQuotation> group) onOpenAzioniDialog;
  final controller = Get.put<RenovationQuotationController>(RenovationQuotationController());

  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 0);

  RenovationQuotationDataSource({
    required this.context,
    required this.groupedQuotations,
    required this.renovators,
    required this.status,
    required this.initialFetchContacts,
    required this.onOpenAzioniDialog,
  });

  @override
  DataRow? getRow(int index) {
    int currentIndex = 0;

    for (var group in groupedQuotations) {
      if (currentIndex == index) {
        return _buildMainQuotationRow(group.first, group);
      }
      currentIndex++;
      if (controller.expandedGroups.contains(group.first.code)) {
        for (int i = 1; i < group.length; i++) {
          bool isLastChild = (i == group.length - 1);
          if (currentIndex == index) {
            return _buildChildQuotationRow(group[i],isLastChild);
          }
          currentIndex++;
        }
      }
    }





    return null;
  }

  associateAddressWithQuotation( RenovationQuotation mainQuotation) async {
    await FirebaseFirestore.instance
    .collection( appConfig.COLLECT_RENOVATION_QUOTATION)
    .doc(mainQuotation.id)
    .update(mainQuotation.toMap());
  }


  DataRow _buildMainQuotationRow(RenovationQuotation mainQuotation, List<RenovationQuotation> group) {
    bool isExpanded = controller.expandedGroups.contains(mainQuotation.code);
    bool isSingleQuotation = group.length == 1;

    // RenovationContactAddress renovationContactAddress = 

    /* Adding a fallback, so that, if there is any address is still not migrated to the new format then it will do that. This code snippet can be removed when everything is migrated to new format. */
    if( (mainQuotation.renovationContactAddressId == null || mainQuotation.renovationContactAddressId == '') && mainQuotation.renovationContact!.addressInfo!.length == 1  ) {
      mainQuotation.renovationContactAddressId = mainQuotation.renovationContact!.addressInfo![0];
      associateAddressWithQuotation(mainQuotation);
    } 
    



    return DataRow2(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
              color: (isExpanded && !isSingleQuotation) ? AppColor.white : AppColor.borderColor,
              width: 1,
          ),
        ),
      ),
      cells: [
        //---- Address + Client Name
        DataCell(
          GestureDetector(
            onTap: () {
              if (isExpanded) {
                controller.expandedGroups.remove(mainQuotation.code);
              } else {
                controller.expandedGroups.add(mainQuotation.code ?? "");
              }
              notifyListeners();
            },
            child: Row(
              children: [
                Icon(isExpanded ? Icons.expand_less : Icons.expand_more, color: Colors.black),
                SizedBox(width: 5),
                Flexible(
                  child: 
                  mainQuotation.renovationContactAddressId != null
                  ? FutureBuilder<RenovationContactAddress>(
                    future: getRenovationContactAddress(mainQuotation.renovationContactAddressId!), 
                    builder: (context, snapshot) {
                      if( snapshot.connectionState == ConnectionState.waiting || snapshot.hasError || !snapshot.hasData || snapshot.data!.id == '' ){
                        return NarFormLabelWidget(
                          // label: "${mainQuotation.renovationContact?.addressInfo?.streetName ?? ""} ${mainQuotation.renovationContact?.addressInfo?.streetNumber ?? ""}, ${mainQuotation.renovationContact?.addressInfo?.city ?? ""} - ${mainQuotation.renovationContact?.personInfo?.name ?? ""} ${mainQuotation.renovationContact?.personInfo?.surname ?? ""}",
                          label: "${mainQuotation.renovationContact?.personInfo?.name ?? ""} ${mainQuotation.renovationContact?.personInfo?.surname ?? ""}",
                          fontSize: 12,
                          fontWeight: '700',
                          overflow: TextOverflow.visible,
                          textAlign: TextAlign.start,
                          textColor: Colors.black,
                        );
                      }
                      // To be worked: Resolved
                      return NarFormLabelWidget(
                        label: snapshot.data!.addressInfo!.toShortAddress() + '-' + "${mainQuotation.renovationContact?.personInfo?.name ?? ""} ${mainQuotation.renovationContact?.personInfo?.surname ?? ""}",
                        fontSize: 12,
                        fontWeight: '700',
                        overflow: TextOverflow.visible,
                        textAlign: TextAlign.start,
                        textColor: Colors.black,
                      );
                    }
                  )
                  : NarFormLabelWidget(
                    // label: "${mainQuotation.renovationContact?.addressInfo?.streetName ?? ""} ${mainQuotation.renovationContact?.addressInfo?.streetNumber ?? ""}, ${mainQuotation.renovationContact?.addressInfo?.city ?? ""} - ${mainQuotation.renovationContact?.personInfo?.name ?? ""} ${mainQuotation.renovationContact?.personInfo?.surname ?? ""}",
                    label: "Address not found - ${mainQuotation.renovationContact?.personInfo?.name ?? ""} ${mainQuotation.renovationContact?.personInfo?.surname ?? ""}",
                    fontSize: 12,
                    fontWeight: '700',
                    overflow: TextOverflow.visible,
                    textAlign: TextAlign.start,
                    textColor: Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ),
        //---- Architetto Name
        DataCell(
          FutureBuilder<String>(
            future: _getArchitectName(mainQuotation.renovationContact?.assignedRenovatorId ?? ""),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                return NarFormLabelWidget(
                  label: snapshot.data,
                  fontSize: 12,
                  fontWeight: '600',
                  textAlign: TextAlign.start,
                  textColor: Colors.black,
                );
              }
              return Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  color: Colors.grey,
                ),
              );
            },
          ),
        ),
        //---- Codice
        DataCell(
        NarFormLabelWidget(
          label: isExpanded ? "${mainQuotation.code ?? ""}_V${mainQuotation.version ?? ""}_R${mainQuotation.revision ?? ""}" : mainQuotation.code ?? "",
          fontSize: 12,
          fontWeight: '600',
          textAlign: TextAlign.start,
          textColor: Colors.black,
        )),
        //---- date
        DataCell(NarFormLabelWidget(
          label: _formatDate(mainQuotation.created),
          fontSize: 12,
          fontWeight: '600',
          textAlign: TextAlign.start,
          textColor: Colors.black,
        )),
        //----comments
        DataCell(
          NarFormLabelWidget(
            label: isExpanded ? mainQuotation.comment : "",
            fontSize: 12,
            fontWeight: '600',
            textAlign: TextAlign.start,
            textColor: Colors.black,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          )
        ),
        //---- status
        DataCell(StatusWidget(
          status: isExpanded ? mainQuotation.status?.replaceAll("-", " ").toTitleCase() : getGroupStatusForNotExpanded(group).replaceAll("-", " ").toTitleCase(),
          statusColor: isExpanded ? CommonUtils.getRenovationQuotationStatusColor(mainQuotation.status ?? "") : CommonUtils.getRenovationQuotationStatusColor(getGroupStatusForNotExpanded(group)),
        )),
        //---- Action
        DataCell(_buildActionsDropdown(mainQuotation,group)),
      ],
    );
  }

  String getGroupStatusForNotExpanded(List<RenovationQuotation> group) {
    bool hasAccettato = group.any((q) => q.status == CommonUtils.accettato);
    bool hasDaModificare = group.any((q) => q.status == CommonUtils.daModificare);
    bool hasRifiutato = group.any((q) => q.status == CommonUtils.rifiutato);

    if (hasAccettato) {
      return CommonUtils.accettato;  // Highest priority
    } else if (hasDaModificare) {
      return CommonUtils.daModificare;  // Second priority
    } else if (hasRifiutato) {
      return CommonUtils.rifiutato;  // Third priority
    }
    return CommonUtils.inAttesa;  // Default fallback
  }

  DataRow _buildChildQuotationRow(RenovationQuotation childQuotation,bool isLastChild) {
    return DataRow2(
      decoration: isLastChild
          ? BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColor.borderColor,
            width: 1,
          ),
        ),
      )
          : null,
      cells: [
        DataCell(Container()),
        DataCell(Container()),
        DataCell(NarFormLabelWidget(
          label: "${childQuotation.code ?? ""}_V${childQuotation.version ?? ""}_R${childQuotation.revision ?? ""}",
          fontSize: 12,
          fontWeight: '600',
          textAlign: TextAlign.start,
          textColor: Colors.black,
        )),
        DataCell(NarFormLabelWidget(
          label: _formatDate(childQuotation.created),
          fontSize: 12,
          fontWeight: '600',
          textAlign: TextAlign.start,
          textColor: Colors.black,
        )),
        //----comments
        DataCell(
          NarFormLabelWidget(
            label: childQuotation.comment,
            fontSize: 12,
            fontWeight: '600',
            textAlign: TextAlign.start,
            textColor: Colors.black,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          )
        ),
        DataCell(StatusWidget(
          status: childQuotation.status?.replaceAll("-", " ").toTitleCase(),
          statusColor: CommonUtils.getRenovationQuotationStatusColor(childQuotation.status ?? ""),
        )),
        DataCell(Container()),
      ],
    );
  }

  Widget _buildActionsDropdown(RenovationQuotation row,List<RenovationQuotation> group) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.0),
      child: NarImageDropdown(
        controller: TextEditingController(),
        customButton: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              NarFormLabelWidget(
                label: "Azioni",
                fontSize: 11,
                fontWeight: '600',
                textColor: AppColor.greyColor,
              ),
              Icon(
                Icons.keyboard_arrow_down,
                color: AppColor.iconGreyColor,
                size: 15,
              )
            ],
          ),
        ),
        options: [
          {
            'value': 'edit',
            'label': 'Modifica',
            'image': 'assets/icons/edit.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'status_modification',
            'label': 'Modifica stato',
            'image': 'assets/icons/dot_icon.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'new_version',
            'label': 'Nuova Versione',
            'image': 'assets/icons/copy.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'new_revision',
            'label': 'Nuova Revisione',
            'image': 'assets/icons/copy.svg',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'download_pdf',
            'label': 'Scarica PDF',
            'image': 'assets/icons/download.png',
            'iconColor': AppColor.greyColor
          },
          {
            'value': 'calculator_pdf',
            'label': 'Gestisci CME',
            'image': 'assets/icons/calculator.svg',
            'iconColor': AppColor.greyColor
          },

          {
            'value': 'comment',
            'label': 'Commenta',
            'image': 'assets/icons/comment.svg',
            'iconColor': AppColor.greyColor
          },{
            'value': 'send_contract',
            'label': 'Gestisci firme',
            'image': 'assets/icons/complete.svg',
            'iconColor': AppColor.greyColor
          },
          if (row.status != 'accettato')
            {
              'value': 'delete',
              'label': 'Elimina',
              'image': 'assets/icons/trash-process.png',
              'iconColor': AppColor.redColor,
              'labelColor': AppColor.redColor
            },
        ],
        iconSize: 15,
        hintText: "Azioni",
        onChanged: (value) {
          if (value == null || value['value'] == null) return;
          onOpenAzioniDialog(row, value['value'].toString().trim(),group);
        },
      ),
    );
  }

  String _formatDate(int? timestamp) {
    if (timestamp == null) return "";
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return "${date.day}/${date.month}/${date.year}";
  }

  Future<String> _getArchitectName(String userId) async {
    NewarcUser user = renovators.firstWhere(
      (element) => element.id == userId,
      orElse: () => NewarcUser({"id": ""}),
    );
    try {
      return "${user.firstName} ${user.lastName}";
    } catch (error) {
      return "";
    }
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount {
    int count = 0;
    for (var group in groupedQuotations) {
      count++;
      if (controller.expandedGroups.contains(group.first.code)) {
        count += group.length - 1;
      }
    }
    return count;
  }


  @override
  int get selectedRowCount => 0;
}



