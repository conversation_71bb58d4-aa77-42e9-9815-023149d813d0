import 'dart:async';
import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:newarc_platform/functions/various.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'dart:html' as html;
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/economics.dart';
import '../../utils/common_utils.dart';

class AbbonamentoView extends StatefulWidget {
  AbbonamentoView({
    super.key,
    this.updateViewCallback,
    required this.agencyUser,
  });

  final Function? updateViewCallback;
  Agency? agencyUser;

  @override
  State<AbbonamentoView> createState() => _AbbonamentoViewState();
}

class _AbbonamentoViewState extends State<AbbonamentoView> {
  List<String> subscription = ["Con Success Fee", "Senza Success Fee"];
  List<Map<String, dynamic>> subscriptions = [];
  List<Map<String, dynamic>> reportSubscriptions = [];
  List<Map<String, dynamic>> smartSubscriptions = [];
  List<String> tabList = ["Con Success Fee", "Senza Success Fee"];
  String errorMsg = "Seleziona un abbonamento";
  String errorMsgForReport = "Seleziona un abbonamento";
  String errorMsgSmart = "Seleziona un abbonamento";
  List<Map<String, dynamic>> finalSubscriptionsList = [];

  /// selected plan - please check with selectedTab index for with Success fee or without Success fee
  Map<String, dynamic> selectedPlan = {};
  Map<String, dynamic> selectedPlanForReport = {};
  Map<String, dynamic> selectedPlanForSmart = {};

  ///selected card index
  int selectedCardIndex = -1;
  ///selected card index Smart
  int selectedCardIndexForSmart = -1;
  ///selected card index Report
  int selectedCardIndexForReport = -1;

  /// Success fee or without Success fee
  int selectedTab = 0;

  bool loading = false;
  bool _isFrozen = false;
  bool paymentError = false;
  bool isMonthly = true;
  bool isSmartMonthly = true;

  bool wasEmailSendingTriggered = false;

  @override
  void initState() {
    super.initState();
    fetchSubscriptions(isMonthly: true);
    fetchSmartSubscriptions(isMonthly: true);
    fetchReportSubscriptions();
  }

  void fetchSubscriptions({required bool isMonthly}) async {
    finalSubscriptionsList = [];
    subscriptions = [];
    final QuerySnapshot result = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_IMMAGINA_SUBSCRIPTION)
        .where('isMonthly', isEqualTo: isMonthly)
        .get();

    final List<Map<String, dynamic>> fetchedData = result.docs.map((doc) {
      return {"id": doc.id, ...doc.data() as Map<String, dynamic>};
    }).toList();

    setState(() {
      subscriptions = fetchedData;
      finalSubscriptionsList =
          subscriptions.where((test) => (test['successFee'] != '0') && (test['planType'] != 'Start') && !(["oldGoldSubscription", "oldStartSubscription"].contains(test["id"]))).toList();
      finalSubscriptionsList.add({
        "id": "custom", 
        "price": 0, // needed to trigger "Contattaci" display in subscriptioncard
        "planType": "Custom", 
        "successFee": "0.2", 
        "servicePerYear": "servizi/mese su richiesta", 
        "services": finalSubscriptionsList[0]['services']
      });
      sortListAndFill(finalSubscriptionsList);
    });
  }

  void fetchReportSubscriptions() async {
    final QuerySnapshot result =
    await FirebaseFirestore.instance.collection(appConfig.COLLECT_REPORT_ACQUIRENTE_SUBCRIPTION).get();

    final List<Map<String, dynamic>> fetchedData = result.docs.map((doc) {
      return {"id": doc.id, ...doc.data() as Map<String, dynamic>};
    }).toList();


    setState(() {
      reportSubscriptions = fetchedData.reversed.toList(); ;
    });
  }

  void fetchSmartSubscriptions({required bool isMonthly}) async {
    smartSubscriptions = [];
    final QuerySnapshot result = await FirebaseFirestore.instance
        .collection(appConfig.COLLECT_IMMAGINA_SMART_SUBSCRIPTION)
        .where('isMonthly', isEqualTo: isMonthly)
        .get();

    final List<Map<String, dynamic>> fetchedData = result.docs.map((doc) {
      return {"id": doc.id, ...doc.data() as Map<String, dynamic>};
    }).toList();

    setState(() {
      smartSubscriptions = fetchedData;
      // smartSubscriptions.add({
      //   "id": "custom", 
      //   "price": 0, // needed to trigger "Contattaci" display in subscriptioncard
      //   "planType": "Custom", 
      //   "successFee": "0.2", 
      //   "servicePerYear": "servizi/mese su richiesta", 
      //   "services": subscriptions[0]['services']
      // });
      sortListAndFill(smartSubscriptions);
    });
  }

  void sortListAndFill(List list) {
    const customOrder = ["Start", "Premium", "Gold", "Supreme", "Custom"];

    list.sort((a, b) {
      int indexA = customOrder.indexOf(a['planType']);
      int indexB = customOrder.indexOf(b['planType']);
      if (indexA == -1) indexA = customOrder.length;
      if (indexB == -1) indexB = customOrder.length;
      return indexA.compareTo(indexB);
    });
  }

  final GlobalKey _planKey = GlobalKey();
  final GlobalKey _reportPlanKey = GlobalKey();
  final GlobalKey _smartPlanKey = GlobalKey();

  final ScrollController _scrollController = ScrollController();

  void scrollToSection(GlobalKey key) {
    Scrollable.ensureVisible(
      key.currentContext!,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    double titlesFontSize = 21;
    double lowerTitlePadding = 40;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.vertical,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                NarFormLabelWidget(
                  label: 'Abbonamenti attivi',
                  fontSize: titlesFontSize,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ],
            ),
            SizedBox(height: lowerTitlePadding),
            _yourSubscription(),
            Padding(
              padding: const EdgeInsets.only(top: 60.0, bottom: 26.0),
              child: Divider(
                height: 2,
                color: Color(0xffCDCDCD),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                NarFormLabelWidget(
                  label: 'Acquista un abbonamento',
                  fontSize: titlesFontSize,
                  fontWeight: '700',
                  textColor: Colors.black,
                ),
              ],
            ),
            SizedBox(height: lowerTitlePadding),
            //-----Immagina
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Column(
                          children: [
                            Image.asset(
                              key: _planKey,
                              height: 50,
                              width: 248,
                              "assets/logo_newarc_immagina_pro.png",
                            ),
                            SizedBox(height: 50,),
                            PaymentTabToggle(
                              isMonthly: isMonthly,
                              onChanged: (value) {
                                fetchSubscriptions(isMonthly: value);
                                setState((){
                                  isMonthly = value;
                                  selectedCardIndex = -1;
                                });
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: 10,),
                    _plans(),
                    // SizedBox(height: 10),
                    Visibility(
                      visible: errorMsg.isNotEmpty,
                      child: Column(
                        children: [
                          NarFormLabelWidget(
                            label: widget.agencyUser?.subscriptionId == null ? errorMsg : "Hai già un abbonamento attivo",
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Color(0xff878787),
                          ),
                          SizedBox(height: 20),
                        ],
                      ),
                    ),
                    _purchasedButton(),
                    SizedBox(height: 40),
                  ],
                ),
              ],
            ),
            SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.only(top: 50.0, bottom: 16),
              child: Divider(
                height: 2,
                color: Color(0xffCDCDCD),
              ),
            ),
            SizedBox(height: 10),
            //-----Immagina Smart
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Column(
                          children: [
                            Image.asset(
                              key: _smartPlanKey,
                              height: 50,
                              width: 248,
                              "assets/logo_newarc_immagina_smart.png",
                            ),
                            SizedBox(height: 50,),
                            PaymentTabToggle(
                              isMonthly: isSmartMonthly,
                              onChanged: (value) {
                                fetchSmartSubscriptions(isMonthly: value);
                                setState((){
                                  isSmartMonthly = value;
                                  selectedCardIndexForSmart = -1;
                                });
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: 10,),
                    _plans(isSmart: true),
                    // SizedBox(height: 10),
                    Visibility(
                      visible: errorMsgSmart.isNotEmpty,
                      child: Column(
                        children: [
                          NarFormLabelWidget(
                            label: widget.agencyUser?.smartSubscriptionId == null ? errorMsgSmart : "Hai già un abbonamento attivo",
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Color(0xff878787),
                          ),
                          SizedBox(height: 20),
                        ],
                      ),
                    ),
                    _purchasedButton(isSmart: true),
                    SizedBox(height: 40),
                  ],
                ),
              ],
            ),
            SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.only(top: 50.0, bottom: 16),
              child: Divider(
                height: 2,
                color: Color(0xffCDCDCD),
              ),
            ),
            SizedBox(height: 10),
            //-----Reports
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          key: _reportPlanKey,
                          height: 27,
                          width: 212,
                          "assets/newarc_report_logo.png",
                        ),
                      ],
                    ),
                    SizedBox(height: 26),
                    _reportPlans(),
                    // SizedBox(height: 20),
                    Visibility(
                      visible: errorMsgForReport.isNotEmpty,
                      child: Column(
                        children: [
                          NarFormLabelWidget(
                            label: widget.agencyUser?.reportSubscriptionId == null ? errorMsgForReport : "Hai già un abbonamento attivo",
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Color(0xff878787),
                          ),
                          SizedBox(height: 20),
                        ],
                      ),
                    ),
                    _purchasedButtonForReport(),
                    SizedBox(height: 40),
                  ],
                ),
              ],
            ),
            SizedBox(height: 10),

          ],
        ),
      ),
    );
  }

  MouseRegion _purchasedButton({bool isSmart = false}) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          if (isSmart) {
            if (selectedCardIndexForSmart == -1) {
              errorMsgSmart = "Seleziona un abbonamento";
              return;
            }
          } else {
            if (selectedCardIndex == -1) {
              errorMsg = "Seleziona un abbonamento";
              return;
            }
          }

          final hasSubscription =
              (widget.agencyUser?.subscriptionId ?? "").isNotEmpty;
          final expiryDate = DateTime.fromMillisecondsSinceEpoch(
              widget.agencyUser?.subscriptionEndDate ?? 0);
          final now = DateTime.now();
          final isSubscriptionActive = expiryDate.isAfter(now);
          final hasServicesLeft =
              widget.agencyUser?.subscriptionServiceCountLeft != 0;

          final hasSmartSubscription = (widget.agencyUser?.smartSubscriptionId ?? "").isNotEmpty;
          final smartExpiryDate = DateTime.fromMillisecondsSinceEpoch(widget.agencyUser?.smartSubscriptionEndDate ?? 0);
          final isSmartSubscriptionActive = smartExpiryDate.isAfter(now);
          final hasSmartServicesLeft = widget.agencyUser?.smartSubscriptionServiceCountLeft != 0;

          if ((hasSubscription && !isSmart) || (hasSmartSubscription && isSmart)) {
            if ((isSubscriptionActive && !isSmart) || (isSmartSubscriptionActive && isSmart)) {
              if ((hasServicesLeft && !isSmart) || (hasSmartServicesLeft && isSmart)) {
                _alreadyHaveSubscriptionDialog();
              } else {
                await _buySubscriptionDialog(
                  value: isSmart ? selectedPlanForSmart : selectedPlan,
                  isWithFee: selectedTab == 0,
                  isSmart: isSmart,
                );
              }
            } else {
              await _buySubscriptionDialog(
                value: isSmart ? selectedPlanForSmart : selectedPlan,
                isWithFee: selectedTab == 0,
                isSmart: isSmart,
              );
            }
          } else {
            await _buySubscriptionDialog(
              value: isSmart ? selectedPlanForSmart : selectedPlan,
              isWithFee: selectedTab == 0,
              isSmart: isSmart,
            );
          }
          setState(() {});
        },
        child: Container(
          height: 43,
          width: 150,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: 
             !isSmart 
             ? selectedCardIndex == -1
                ? Colors.black.withOpacity(0.5)
                : Colors.black
             : selectedCardIndexForSmart == -1
                ? Colors.black.withOpacity(0.5)
                : Colors.black,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: NarFormLabelWidget(
              label: 'Acquista',
              fontSize: 15,
              fontWeight: '600',
              textColor: AppColor.white,
            ),
          ),
        ),
      ),
    );
  }

  MouseRegion _purchasedButtonForReport() {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () async {
          if (selectedCardIndexForReport == -1) {
            errorMsgForReport = "Seleziona un abbonamento";
            // setState(() {});
            return;
          }

          final hasSubscription = (widget.agencyUser?.reportSubscriptionId ?? "").isNotEmpty;
          final expiryDate = DateTime.fromMillisecondsSinceEpoch(widget.agencyUser?.reportSubscriptionEndDate ?? 0);
          final now = DateTime.now();
          final isSubscriptionActive = expiryDate.isAfter(now);


          if (hasSubscription) {
            if (isSubscriptionActive) {
              _alreadyHaveSubscriptionDialog();
            } else {
              await _buySubscriptionDialogForAgencyReport(
                value: selectedPlanForReport,
              );
            }
          } else {
            await _buySubscriptionDialogForAgencyReport(
              value: selectedPlanForReport,
            );
          }
          setState(() {});
        },
        child: Container(
          height: 43,
          width: 150,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: selectedCardIndexForReport == -1
                ? Colors.black.withOpacity(0.5)
                : Colors.black,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: NarFormLabelWidget(
              label: 'Acquista',
              fontSize: 15,
              fontWeight: '600',
              textColor: AppColor.white,
            ),
          ),
        ),
      ),
    );
  }

  _successPaymentDialog(context, {required bool isReport, bool isSmart = false}) {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: BaseNewarcPopup(
            noButton: true,
            title: "Acquista il tuo abbonamento",
            column: Container(
              width: 400,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [

                  Image.asset(
                    height: isReport ? 27 : 50,
                    width: isReport ? 212 : null,
                    "assets/${isReport ? "newarc_report_logo.png":"immagina-logo-esteso.png"}",
                  ),
                  SizedBox(height: 20),
                  NarFormLabelWidget(
                    label: 'Il tuo abbonamento Newarc ${isReport ? "Reports" : isSmart ? "Immagina Smart" : "Immagina Pro"} è stato attivato!',
                    fontSize: 20,
                    textAlign: TextAlign.center,
                    fontWeight: '600',
                    textColor: AppColor.black,
                    overflow: TextOverflow.clip,
                  ),
                  SizedBox(height: 50),
                  BaseNewarcButton(
                    width: 142,
                    textColor: AppColor.white,
                    color: Theme.of(context).primaryColor,
                    buttonText: "Chiudi",
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // _payByBankTransferDialog({dynamic value, required Agency agency}) {
  //   return showDialog(
  //     context: context,
  //     builder: (context) {
  //       return Center(
  //         child: BaseNewarcPopup(
  //           noButton: true,
  //           title: "Acquista il tuo abbonamento",
  //           column: Container(
  //             // height: 305,
  //             width: 489,
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               mainAxisSize: MainAxisSize.min,
  //               children: [
  //                 SvgPicture.asset(
  //                   height: 50,
  //                   "assets/newarc_immagina.svg",
  //                 ),
  //                 SizedBox(height: 26),
  //                 Container(
  //                   padding: EdgeInsets.all(18),
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(10),
  //                     color: AppColor.white,
  //                     border: Border.all(
  //                       width: 1,
  //                       color: Color(0xffDBDBDB),
  //                     ),
  //                   ),
  //                   child: Column(
  //                     children: [
  //                       NarFormLabelWidget(
  //                         label:
  //                             "${value['plan_type']} ${(value["success_fee"] != null && value["success_fee"] != "0") ? "+ Success Fee" : ""}",
  //                         fontSize: 17,
  //                         fontWeight: '700',
  //                         textColor: Theme.of(context).primaryColor,
  //                       ),
  //                       SizedBox(
  //                         height: 16,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         isHaveBorder: false,
  //                         isCenterLabel: true,
  //                         flex: 0,
  //                         suffixIcon: null,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "Intestare bonifico a",
  //                         controller: TextEditingController(
  //                           text: "Newarc Srl",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 10,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         suffixIcon: null,
  //                         isHaveBorder: false,
  //                         isCenterLabel: true,
  //                         flex: 0,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "IBAN",
  //                         controller: TextEditingController(
  //                           text: "***************************",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 10,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         suffixIcon: null,
  //                         isCenterLabel: true,
  //                         isHaveBorder: false,
  //                         flex: 0,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "Somma da bonificare",
  //                         controller: TextEditingController(
  //                           text: "${
  //                               (value['price'] ?? 0) * 1.22 == 0
  //                               ? "" 
  //                               : ((value['price'] ?? 0) * 1.22).toStringAsFixed(2)
  //                             }€",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 10,
  //                       ),
  //                       CustomTextFormField(
  //                         textAlign: TextAlign.center,
  //                         isCenterLabel: true,
  //                         isHaveBorder: false,
  //                         flex: 0,
  //                         suffixIcon: null,
  //                         fillColor: Color(0xffF5F5F5),
  //                         readOnly: true,
  //                         label: "Causale",
  //                         controller: TextEditingController(
  //                           text: "Acquisto Abbonamento Immagina",
  //                         ),
  //                       ),
  //                       SizedBox(
  //                         height: 20,
  //                       ),
  //                       NarFormLabelWidget(
  //                         label:
  //                             'Istituto bancario: Intesa San Paolo\nPiazza Massua 5, 10141, Torino',
  //                         fontSize: 12,
  //                         fontWeight: '600',
  //                         textAlign: TextAlign.center,
  //                         textColor: Color(0xff818181),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //                 SizedBox(height: 48),
  //                 NarFormLabelWidget(
  //                   label:
  //                       'Le stesse informazioni sono state inviate via email.',
  //                   fontSize: 12,
  //                   fontWeight: '600',
  //                   textColor: Color(0xff818181),
  //                 ),
  //                 SizedBox(height: 10),
  //                 BaseNewarcButton(
  //                   width: 142,
  //                   textColor: AppColor.white,
  //                   color: Theme.of(context).primaryColor,
  //                   buttonText: "Chiudi",
  //                   onPressed: () async {
  //                     Navigator.pop(context);
  //                     _successPaymentDialog();
  //                   },
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  Future<void> _alreadyHaveSubscriptionDialog() {
    return showDialog(
      context: context,
      builder: (context) {
        return Center(
          child: BaseNewarcPopup(
            noButton: false,
            onPressed: () {},
            buttonText: "Chiudi",
            title: "Acquista il tuo abbonamento",
            column: Container(
              width: 400,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 30.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NarFormLabelWidget(
                      label: 'Hai già un abbonamento attivo!',
                      fontSize: 20,
                      fontWeight: '600',
                      textColor: AppColor.black,
                    ),
                    SizedBox(
                      height: 13,
                    ),
                    NarFormLabelWidget(
                      label: '<NAME_EMAIL>\nper modificare il tuo piano.',
                      fontSize: 16,
                      fontWeight: '600',
                      textAlign: TextAlign.center,
                      textColor: Color(0xff616161),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _buySubscriptionDialog({dynamic value, bool isWithFee = true, bool isSmart = false}) {
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Stack(
              children: [
                Center(
                  child: BaseNewarcPopup(
                    noButton: true,
                    title: "Acquista il tuo abbonamento",
                    column: Container(
                      // height: 305,
                      width: 489,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            '${isSmart ? "assets/logo_newarc_immagina_smart.png" : "assets/logo_newarc_immagina_pro.png"}',
                            height: 27,
                            width: 200,
                          ),
                          SizedBox(height: 26),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 10),
                            padding: EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(color: Color(0xFFC3C3C3),width: 1)
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Abbonamento selezionato',
                                        fontSize: 12,
                                        fontWeight: '500',
                                        textColor: Color(0xff828282),
                                      ),
                                      SizedBox(height: 6),
                                      NarFormLabelWidget(
                                        label: "${value['planType']}",
                                        fontSize: 17,
                                        fontWeight: '700',
                                      ),
                                      SizedBox(height: 3),
                                      NarFormLabelWidget(
                                        label: 'Durata: ${value['duration']}',
                                        fontSize: 12,
                                        fontWeight: '500',
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 60,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: VerticalDivider(
                                      color: Color(0xffC3C3C3),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Crediti disponibili',
                                        fontSize: 12,
                                        fontWeight: '500',
                                        textColor: Color(0xff828282),
                                      ),
                                      SizedBox(height: 6),
                                      NarFormLabelWidget(
                                        label: '${value['serviceCount']}',
                                        fontSize: 18,
                                        fontWeight: '600',
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 30),
                          paymentError
                              ? Padding(
                                  padding: const EdgeInsets.only(bottom: 10.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "Qualcosa è andato storto con il pagamento, riprova.",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontFamily: 'Raleway-500',
                                            fontSize: 14,
                                            color: Colors.black),
                                      ),
                                    ],
                                  ),
                                )
                              : SizedBox(),
                          loading
                              ? CircularProgressIndicator(
                                  color: Theme.of(context).primaryColor)
                              : Row(
                                  children: [
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 20),
                                        child: BaseNewarcButton(
                                          buttonText: "Paga ora",
                                          onPressed: () async {
                                            setState(() {
                                              loading = true;
                                              _isFrozen = true;
                                            });

                                            // open tab before async calls so to avoid iOS popup blocking
                                            final newTab = html.window.open('', '_blank');

                                            final docRef = FirebaseFirestore.instance
                                                .collection(appConfig.COLLECT_AGENCIES)
                                                .doc(widget.agencyUser!.id);
                                            
                                            Map<String, dynamic> stripePriceIdsMap = {
                                              value['stripePriceId']: 1,
                                            };

                                            bool wasPaymentDetected = false;
                                            bool wasEmailSendingTriggered = false;

                                            try {
                                              var linkMap = await getStripeCheckoutLink(
                                                stripePriceIdsMap,
                                                null,
                                                widget.agencyUser!.id!,
                                                origin: isSmart ? "abbonamento_smart" : "abbonamento_pro",
                                              );

                                              if (linkMap['link'] == null) {
                                                setState(() {
                                                  loading = false;
                                                  paymentError = true;
                                                  _isFrozen = false;
                                                });
                                                newTab.close();
                                                return;
                                              }

                                              // Assign the Stripe payment link to the tab
                                              newTab.location.href = linkMap['link']!;

                                              // Check if the user closed the tab without completing payment
                                              Future.doWhile(() async {
                                                await Future.delayed(Duration(seconds: 1));
                                                return !wasPaymentDetected && newTab.closed == false;
                                              }).then((_) {
                                                if (!wasPaymentDetected) {
                                                  setState(() {
                                                    _isFrozen = false;
                                                    loading = false;
                                                    paymentError = true;
                                                  });
                                                }
                                              });

                                              // Firestore listener for successful payment
                                              Future.delayed(Duration(seconds: 1), () {
                                                StreamSubscription? subscription;
                                                subscription = docRef
                                                    .snapshots()
                                                    .listen((event) async {
                                                  final data = event.data() ?? {};
                                                  log("Listener fired!");
                                                  var agencyPaymentDate = isSmart ? (data['smartSubscriptionPaymentDate'] ?? 0) : (data['subscriptionPaymentDate'] ?? 0);
                                                  agencyPaymentDate = DateTime.fromMillisecondsSinceEpoch(agencyPaymentDate);
                                                  // if agencyPaymentDate day is not today skip it
                                                  if (agencyPaymentDate.day != DateTime.now().day) {
                                                    log("Payment date is not today, skipping");
                                                    return;
                                                  }
                                                  if (wasPaymentDetected) {
                                                    log("Payment already detected, skipping duplicate processing");
                                                    return;
                                                  }
                                                  wasPaymentDetected = true;
                                                  log("Payment detected, processing subscription activation");

                                                  if (wasEmailSendingTriggered) {
                                                    log("Email sending already detected, skipping duplicate processing");
                                                    return;
                                                  }
                                                  wasEmailSendingTriggered = true;
                                                  log("Payment detected, processing subscription activation");
                                                  
                                                  // Send customer notification email
                                                  try {
                                                    await sendEmail(
                                                      templateId: CommonUtils.subscriptionActivatedEmailTemplateId,
                                                      subject: CommonUtils.subscriptionActivatedEmailSubject,
                                                      recipientEmail: widget.agencyUser!.email,
                                                      recipientName: widget.agencyUser!.name,
                                                    );
                                                    log("Customer notification email sent successfully");
                                                  } catch (e) {
                                                    log("Error sending customer notification email: $e");
                                                  }

                                                  // Send internal notification emails
                                                  try {
                                                    String subscriptionName = "Immagina Pro - ${value['planType']} ${(value["successFee"] != null && value["successFee"] != "0") ? "+ Success Fee" : ""} - ${isMonthly ? "Mensile" : "Annuale"}";
                                                    String smartSubscriptionName = "Immagina Smart - ${value['planType']} - ${isMonthly ? "Mensile" : "Annuale"}";

                                                    Map<String, dynamic> workEmailVariables = {
                                                      'agencyname': widget.agencyUser!.name,
                                                      'subscriptionname': isSmart ? smartSubscriptionName : subscriptionName,
                                                    };

                                                    final masterUsers = await FirebaseFirestore.instance
                                                      .collection(appConfig.COLLECT_USERS)
                                                      .where('type', isEqualTo: 'newarc')
                                                      .where('role', isEqualTo: 'master')
                                                      .where('isActive', isEqualTo: true)
                                                      .get();

                                                    for (var userDoc in masterUsers.docs) {
                                                      final userData = userDoc.data();
                                                      if (userData['email'] != null) {
                                                        try {
                                                          await sendEmail(
                                                            templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                                            subject: CommonUtils.subscriptionActivatedForWorkSideEmailSubject,
                                                            variables: workEmailVariables,
                                                            recipientEmail: userData['email'],
                                                            recipientName: userData['firstName'] != null
                                                                ? "${userData['firstName']} ${userData['lastName'] ?? ''}"
                                                                : "Master",
                                                          );
                                                        } catch (e) {
                                                          log("Error sending email to master ${userData['email']}: $e");
                                                        }
                                                      }
                                                    }
                                                    if (appConfig.isProduction) {
                                                      try {
                                                        await sendEmail(
                                                          templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                                          subject: CommonUtils.subscriptionActivatedForWorkSideEmailSubject,
                                                          variables: workEmailVariables,
                                                          recipientEmail: "<EMAIL>",
                                                          recipientName: "Amministrazione",
                                                        );
                                                      } catch (e) {
                                                        log("Error sending email to amministrazione: $e");
                                                      }
                                                    }
                                                    log("Internal notification emails sent successfully");
                                                  } catch (e) {
                                                    log("Error sending internal notification emails: $e");
                                                  }

                                                    setState(() {
                                                      loading = false;
                                                      _isFrozen = false;
                                                      paymentError = false;
                                                    });

                                                    widget.agencyUser =
                                                        Agency.fromDocument(event.data()!, event.id);

                                                    subscription?.cancel();

                                                    Navigator.of(context).pop();
                                                    _successPaymentDialog(context, isReport: false, isSmart: isSmart);
                                                },
                                                onError: (error) {
                                                  log("-------ERROR ${error.toString()}");
                                                  setState(() {
                                                    loading = false;
                                                    _isFrozen = false;
                                                    paymentError = false;
                                                  });
                                                  subscription?.cancel();
                                                });
                                              });
                                            } catch (e, stackTrace) {
                                              log("-------CATCH ERROR ${e.toString()}");
                                              log("-------CATCH stackTrace ${stackTrace.toString()}");
                                              setState(() {
                                                loading = false;
                                                paymentError = true;
                                                _isFrozen = false;
                                              });
                                              newTab.close();
                                            }
                                          }
                                        ),
                                      ),
                                    ),
                                    // Expanded(
                                    //   child: Padding(
                                    //     padding: const EdgeInsets.symmetric(
                                    //         horizontal: 20),
                                    //     child: BaseNewarcButton(
                                    //       textColor: Theme.of(context).primaryColor,
                                    //       color: AppColor.white,
                                    //       borderColor: Theme.of(context).primaryColor,
                                    //       buttonText: "Paga con bonifico",
                                    //       onPressed: () async{
                                    //         setState(() {
                                    //           loading = true;
                                    //         });
                                    //         try {
                                    //           final collectionRef = FirebaseFirestore.instance.collection(appConfig.COLLECT_AGENCIES);
                                    //           // Update the field
                                    //           await collectionRef.doc(widget.agencyUser?.id).update({
                                    //             'subscriptionPaymentDate': null,
                                    //             'subscriptionId': value["id"],
                                    //             'subscriptionEndDate': DateTime.now().add(Duration(days: 365)).millisecondsSinceEpoch,
                                    //             'subscriptionServiceCount': value["service_count"],
                                    //             'subscriptionServiceCountLeft': value["service_count"],
                                    //             'subscriptionStartDate': DateTime.now().millisecondsSinceEpoch,
                                    //           });
                                    //           // bank transfer and subscription activate send notification mail
                                    //           Map<String,dynamic> emailVariable = {
                                    //             'subscriptionname':value["plan_type"],
                                    //             'subscriptionprice':"${
                                    //               (value['price'] ?? 0) * 1.22 == 0
                                    //               ? "" 
                                    //               : ((value['price'] ?? 0) * 1.22).toStringAsFixed(2)
                                    //             }€",
                                    //           };
                                    //           sendEmail(templateId: CommonUtils.bankTransferEmailTemplateId, agency: widget.agencyUser!, subject: CommonUtils.bankTransferEmailSubject,variables: emailVariable);
                                    //           sendEmail(templateId: CommonUtils.subscriptionActivatedEmailTemplateId, agency: widget.agencyUser!, subject: CommonUtils.subscriptionActivatedEmailSubject);
                                    //           // send email to masters and amministrazione
                                    //           Map<String, dynamic> workEmailVariables = {
                                    //             'agencyname': widget.agencyUser!.name,
                                    //             'subscriptionname': "${value['plan_type']} ${(value["success_fee"] != null && value["success_fee"] != "0") ? "+ Success Fee" : ""}",
                                    //           };
                                    //           final masterUsers = await FirebaseFirestore.instance
                                    //             .collection(appConfig.COLLECT_USERS)
                                    //             .where('type', isEqualTo: 'newarc')
                                    //             .where('role', isEqualTo: 'master')
                                    //             .where('isActive', isEqualTo: true)
                                    //             .get();
                                    //           for (var userDoc in masterUsers.docs) {
                                    //             final userData = userDoc.data();
                                    //             if (userData['email'] != null) {
                                    //               sendEmail(
                                    //                 agency: widget.agencyUser!,
                                    //                 templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                    //                 subject: CommonUtils.subscriptionActivatedEmailSubject,
                                    //                 variables: workEmailVariables,
                                    //                 recipientEmail: userData['email'],
                                    //                 recipientName: userData['firstName'] != null
                                    //                     ? "${userData['firstName']} ${userData['lastName'] ?? ''}"
                                    //                     : "Master",
                                    //               );
                                    //             }
                                    //           }
                                    //           sendEmail(
                                    //             agency: widget.agencyUser!,
                                    //             templateId: CommonUtils.subscriptionActivatedForWorkSideEmailTemplateId,
                                    //             subject: CommonUtils.subscriptionActivatedEmailSubject,
                                    //             variables: workEmailVariables,
                                    //             recipientEmail: "<EMAIL>",
                                    //             recipientName: "Amministrazione",
                                    //           );
                                              
                                    //           Navigator.pop(context);
                                    //           Future.delayed(Duration(milliseconds: 100));
                                    //           _payByBankTransferDialog(
                                    //               value: value,
                                    //               agency: widget.agencyUser!);
                                    //           log("Document updated subscription count successfully!");
                                    //         } catch (e) {
                                    //           log("Error updating subscription count document: $e");
                                    //           setState(() {
                                    //             loading = false;
                                    //           });
                                    //         }finally{
                                    //           setState(() {
                                    //             loading = false;
                                    //           });
                                    //         }

                                    //       },
                                    //     ),
                                    //   ),
                                    // ),
                                  ],
                                ),
                          SizedBox(height: 22),
                          (value['successFee'] != '0' && !isSmart) ?
                          NarFormLabelWidget(
                            label:
                                'La Success Fee sarà fatturata dopo la chiusura di ogni operazione.',
                            fontSize: 12,
                            fontWeight: '600',
                            textColor: Color(0xff818181),
                          ) : SizedBox(),
                        ],
                      ),
                    ),
                  ),
                ),
                if (_isFrozen)
                  Positioned.fill(
                    child: Container(
                      color: Colors.black54, // Semi-transparent overlay
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _buySubscriptionDialogForAgencyReport({dynamic value}) {

    String durationTitle = value['duration'] == "12 mese" ? "Rinnovo: annuale" : value['duration'] == "1 mese" ? "Rinnovo: mensile" : "";
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Stack(
              children: [
                Center(
                  child: BaseNewarcPopup(
                    noButton: true,
                    title: "Acquista il tuo abbonamento",
                    column: Container(
                      // height: 305,
                      width: 489,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                            height: 27,
                            width: 212,
                            "assets/newarc_report_logo.png",
                          ),
                          SizedBox(height: 26),
                          Container(
                            margin: EdgeInsets.symmetric(horizontal: 10),
                            padding: EdgeInsets.all(18),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(width: 1,color: Color(0xFFC3C3C3)),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Abbonamento selezionato',
                                        fontSize: 12,
                                        fontWeight: '500',
                                        textColor: Color(0xff828282),
                                      ),
                                      SizedBox(height: 6),
                                      NarFormLabelWidget(
                                        label:
                                        "Newarc Reports",
                                        fontSize: 17,
                                        fontWeight: '700',
                                      ),
                                      SizedBox(height: 3),
                                      NarFormLabelWidget(
                                        label: durationTitle,
                                        fontSize: 12,
                                        fontWeight: '500',
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 30),
                          paymentError
                              ? Padding(
                            padding: const EdgeInsets.only(bottom: 10.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  "Qualcosa è andato storto con il pagamento, riprova.",
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontFamily: 'Raleway-500',
                                      fontSize: 14,
                                      color: Colors.black),
                                ),
                              ],
                            ),
                          )
                              : SizedBox(),
                          loading
                              ? CircularProgressIndicator(
                              color: Theme.of(context).primaryColor)
                              : Row(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20),
                                  child: BaseNewarcButton(
                                      buttonText: "Paga ora",
                                      onPressed: () async {
                                        setState(() {
                                          loading = true;
                                          _isFrozen = true;
                                        });

                                        // open tab before async calls so to avoid iOS popup blocking
                                        final newTab = html.window.open('', '_blank');

                                        final docRef = FirebaseFirestore.instance
                                            .collection(appConfig.COLLECT_AGENCIES)
                                            .doc(widget.agencyUser!.id);

                                        List<String> stripePriceIds = [value['stripePriceId']];

                                        bool wasPaymentDetected = false;
                                        bool wasEmailSendingTriggered = false;

                                        try {
                                          var linkMap = await getStripeCheckoutLinkForAgencyReport(
                                            stripePriceIds: stripePriceIds,
                                            userId: widget.agencyUser!.id!,
                                            userType: "agency",
                                            origin: "buyer_report",
                                          );

                                          if (linkMap['link'] == null) {
                                            setState(() {
                                              loading = false;
                                              paymentError = true;
                                              _isFrozen = false;
                                            });
                                            newTab.close();
                                            return;
                                          }

                                          // Assign the Stripe payment link to the tab
                                          newTab.location.href = linkMap['link']!;

                                          // Check if the user closed the tab without completing payment
                                          Future.doWhile(() async {
                                            await Future.delayed(Duration(seconds: 1));
                                            return !wasPaymentDetected && newTab.closed == false;
                                          }).then((_) {
                                            if (!wasPaymentDetected) {
                                              setState(() {
                                                _isFrozen = false;
                                                loading = false;
                                                paymentError = true;
                                              });
                                            }
                                          });

                                          // Firestore listener for successful payment of agency report
                                          Future.delayed(Duration(seconds: 1), () {
                                            StreamSubscription? reportSubscription;
                                            reportSubscription = docRef
                                                .snapshots()
                                                .listen((event) async {
                                                final data = event.data() ?? {};
                                                log("Listener fired!");
                                                var agencyPaymentDate = (data['reportSubscriptionPaymentDate'] ?? 0);
                                                agencyPaymentDate = DateTime.fromMillisecondsSinceEpoch(agencyPaymentDate);
                                                // if agencyPaymentDate day is not today skip it
                                                if (agencyPaymentDate.day != DateTime.now().day) {
                                                  log("Payment date is not today, skipping");
                                                  return;
                                                }
                                                if (wasPaymentDetected) {
                                                  log("Payment already detected, skipping duplicate processing");
                                                  return;
                                                }
                                                wasPaymentDetected = true;
                                                log("Payment detected, processing subscription activation");

                                                if (wasEmailSendingTriggered) {
                                                  log("Email sending already detected, skipping duplicate processing");
                                                  return;
                                                }
                                                wasEmailSendingTriggered = true;
                                                log("Payment detected, processing subscription activation");
                                                // Send customer notification email
                                                try {
                                                  await sendEmail(
                                                    templateId: CommonUtils.reportSubscriptionActivatedEmailTemplateId,
                                                    recipientEmail: widget.agencyUser!.email,
                                                    recipientName: widget.agencyUser!.name,
                                                    subject: CommonUtils.reportSubscriptionActivatedEmailSubject,
                                                  );
                                                  log("Report customer notification email sent successfully");
                                                } catch (e) {
                                                  log("Error sending report customer notification email: $e");
                                                }

                                                // Send internal notification emails
                                                try {
                                                  Map<String, dynamic> workEmailVariables = {
                                                    'agencyname': widget.agencyUser!.name,
                                                    'subscriptionname': "Newarc Reports",
                                                  };
                                                  final masterUsers = await FirebaseFirestore.instance
                                                      .collection(appConfig.COLLECT_USERS)
                                                      .where('type', isEqualTo: 'newarc')
                                                      .where('role', isEqualTo: 'master')
                                                      .where('isActive', isEqualTo: true)
                                                      .get();
                                                  for (var userDoc in masterUsers.docs) {
                                                    final userData = userDoc.data();
                                                    if (userData['email'] != null) {
                                                      try {
                                                        await sendEmail(
                                                          templateId: CommonUtils.reportSubscriptionActivatedEmailTemplateIdForWorkSide,
                                                          subject: CommonUtils.reportSubscriptionActivatedEmailSubjectForWorkSide,
                                                          variables: workEmailVariables,
                                                          recipientEmail: userData['email'],
                                                          recipientName: userData['firstName'] != null
                                                              ? "${userData['firstName']} ${userData['lastName'] ?? ''}"
                                                              : "Master",
                                                        );
                                                      } catch (e) {
                                                        log("Error sending report email to master ${userData['email']}: $e");
                                                      }
                                                    }
                                                  }
                                                  if (appConfig.isProduction) {
                                                    try {
                                                      await sendEmail(
                                                        templateId: CommonUtils.reportSubscriptionActivatedEmailTemplateIdForWorkSide,
                                                        subject: CommonUtils.reportSubscriptionActivatedEmailSubjectForWorkSide,
                                                        variables: workEmailVariables,
                                                        recipientEmail: "<EMAIL>",
                                                        recipientName: "Amministrazione",
                                                      );
                                                    } catch (e) {
                                                      log("Error sending report email to amministrazione: $e");
                                                    }
                                                  }
                                                  log("Report internal notification emails sent successfully");
                                                } catch (e) {
                                                  log("Error sending report internal notification emails: $e");
                                                }

                                                // Update UI state only if widget is still mounted
                                                setState(() {
                                                  loading = false;
                                                  _isFrozen = false;
                                                  paymentError = false;
                                                });

                                                widget.agencyUser = Agency.fromDocument(event.data()!, event.id);

                                                reportSubscription?.cancel();

                                                Navigator.of(context).pop();
                                                _successPaymentDialog(context, isReport: true);
                                            },
                                                onError: (error) {
                                                  log("-------ERROR while paying for Reports ${error.toString()}");

                                                  setState(() {
                                                    loading = false;
                                                    _isFrozen = false;
                                                    paymentError = false;
                                                  });

                                                  reportSubscription?.cancel();
                                                });
                                          });
                                        } catch (e, stackTrace) {
                                          log("-------CATCH ERROR ${e.toString()}");
                                          log("-------CATCH stackTrace ${stackTrace.toString()}");
                                          setState(() {
                                            loading = false;
                                            paymentError = true;
                                            _isFrozen = false;
                                          });
                                          newTab.close();
                                        }
                                      }
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 22),
                        ],
                      ),
                    ),
                  ),
                ),
                if (_isFrozen)
                  Positioned.fill(
                    child: Container(
                      color: Colors.black54, // Semi-transparent overlay
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _plans({bool isSmart = false}) {
    return SizedBox(
      // key: isSmart ? _smartPlanKey : _planKey,
      height: 455,
      child: Padding(
        padding: const EdgeInsets.only(right: 30.0,bottom: 10.0),
        child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: isSmart ? smartSubscriptions.length : finalSubscriptionsList.length,
          itemBuilder: (context, index) {
            final subscription = isSmart ? smartSubscriptions[index] : finalSubscriptionsList[index];
            return SubscriptionCard(
              agencyUser: widget.agencyUser,
              selectedTab: isSmart ? 1 : selectedTab,
              subscription: subscription,
              isSelected: isSmart ? selectedCardIndexForSmart == index : selectedCardIndex == index,
              onTap: isSmart
              ? widget.agencyUser?.smartSubscriptionId == null
                ?  () {
                    errorMsgSmart = "";
                    selectedPlanForSmart = subscription;
                    selectedCardIndexForSmart = index;
                    setState(() {});
                  }
                : (){}
              : widget.agencyUser?.subscriptionId == null
              ?  () {
                  errorMsg = "";
                  selectedPlan = subscription;
                  selectedCardIndex = index;
                  setState(() {});
                }
              : (){},
            );
          },
        ),
      ),
    );
  }

  Widget _reportPlans() {
    return SizedBox(
      // key: _reportPlanKey,
      height: 380,
      child: Padding(
        padding: const EdgeInsets.only(right: 30.0,bottom: 30.0),
        child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: reportSubscriptions.length,
          itemBuilder: (context, index) {
            final reportSubscriptionItem = reportSubscriptions[index];
            return ReportCard(
              agencyUser: widget.agencyUser,
              subscription: reportSubscriptionItem,
              isSelected: selectedCardIndexForReport == index,
              onTap: () {
                  errorMsgForReport = "";
                  selectedPlanForReport = reportSubscriptionItem;
                  selectedCardIndexForReport = index;
                  setState(() {});
              },
            );
          },
        ),
      ),
    );
  }

  Container _subscrionTabBar() {
    return Container(
      padding: EdgeInsets.all(2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Color(0xffF2F2F2),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: tabList.map((element) {
          int index = tabList.indexOf(element);
          return MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                selectedTab = index;
                selectedCardIndex = -1;
                selectedPlan = {};
                if (selectedTab == 0) {
                  finalSubscriptionsList = subscriptions
                      .where((test) => test['successFee'] != '0')
                      .toList();
                } else {
                  finalSubscriptionsList = subscriptions
                      .where((test) => test['successFee'] == '0')
                      .toList();
                }
                sortListAndFill(finalSubscriptionsList);
                setState(() {});
              },
              child: Container(
                width: 170,
                height: 44,
                decoration: BoxDecoration(
                  color:
                      selectedTab == index ? AppColor.white : Color(0xffF2F2F2),
                  border: selectedTab == index
                      ? Border.all(
                          width: 1,
                          color: Color(0xffDEDEDE),
                        )
                      : null,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NarFormLabelWidget(
                        label: element.split(' ').first,
                        fontSize: 15,
                        fontWeight: '700',
                        textColor: selectedTab == index
                            ? Theme.of(context).primaryColor
                            : Color(0xff7C7C7C),
                      ),
                      NarFormLabelWidget(
                        label: ' ${element.split(' ').sublist(1).join(' ')}',
                        fontSize: 15,
                        fontWeight: '700_italic',
                        textColor: selectedTab == index
                            ? Theme.of(context).primaryColor
                            : Color(0xff7C7C7C),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _subscriptionStatusContainer(String subscriptionType, String subscriptionId, bool hasHadSubscription, DateTime expiryDate, int subscriptionServiceCount, int subscriptionServiceCountLeft, planKey){

    double subscriptionContainersWidths = 350;
    // check subscriptionType is among "report", "smart", "pro"
    if ((subscriptionType != "report")&&(subscriptionType != "smart")&&(subscriptionType != "pro")){
      return SizedBox.shrink();
    }
    String subscriptionCollectionName = subscriptionType == "report" ? appConfig.COLLECT_REPORT_ACQUIRENTE_SUBCRIPTION : subscriptionType == "smart" ? appConfig.COLLECT_IMMAGINA_SMART_SUBSCRIPTION : appConfig.COLLECT_IMMAGINA_SUBSCRIPTION;
    String logoPath = subscriptionType == "report" ? "assets/newarc_report_logo.png" : subscriptionType == "smart" ? "assets/logo_newarc_immagina_smart.png" : "assets/logo_newarc_immagina_pro.png";
    bool isExpired = DateTime.now().isAfter(expiryDate);
    bool isServiceOver = subscriptionServiceCountLeft <= 0;

    return Container(
      width: subscriptionContainersWidths,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: !hasHadSubscription ? Color(0xFFF6F6F6) :AppColor.white,
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          width: 1,
          color: !hasHadSubscription ? Colors.transparent : Color(0xffCFCFCF),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Opacity(
                opacity: !hasHadSubscription ? 0.5 : 1.0,
                child: Padding(
                  padding: EdgeInsets.only(top: subscriptionType == "report" ? 10.0 : 0.0),
                  child: Image.asset(
                    height: 40,
                    width: subscriptionType == "report" ? 150 : 200,
                    logoPath,
                  ),
                ),
              ),
            ],
          ),
            SizedBox(
              height: 30,
            ),
            Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: !hasHadSubscription ? null : Color(0xFFF6F6F6),
                borderRadius: BorderRadius.circular(10),
                border: Border.all(
                  color: Colors.transparent, 
                  width: 1)
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!hasHadSubscription) ...[
                    Expanded(
                      flex: 2,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          NarFormLabelWidget(
                            label: 'Nessun abbonamento attivo',
                            fontSize: 12,
                            fontWeight: '500',
                            textColor: Color(0xff989898),
                          ),
                        ],
                      ),
                    ),
                  ] else ...[
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: subscriptionType != "report" ? CrossAxisAlignment.start : CrossAxisAlignment.center,
                      children: [
                        NarFormLabelWidget(
                          label: 'Abbonamento acquistato',
                          fontSize: 12,
                          fontWeight: '500',
                          textColor: Color(0xff828282),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        FutureBuilder<
                            DocumentSnapshot<Map<String, dynamic>>>(
                          future: FirebaseFirestore.instance
                              .collection(subscriptionCollectionName)
                              .doc(subscriptionId)
                              .get(),
                          builder: (context, futureSnapshot) {
                            if (futureSnapshot.connectionState ==
                                ConnectionState.waiting) {
                              return SizedBox();
                            }

                            if (futureSnapshot.hasError ||
                                !futureSnapshot.hasData ||
                                !futureSnapshot.data!.exists) {
                              return SizedBox();
                            }

                            final subscriptionData = futureSnapshot.data!.data();
                            String planName = subscriptionData?['planType'] ?? 'Unknown Plan';
                            // bool isSuccessFee = subscriptionData?['successFee'] != '0';
                            // String planType = isSuccessFee ? ' + Success Fee' : '';
                            return NarFormLabelWidget(
                              label: planName, //  + planType,
                              fontSize: 17,
                              fontWeight: '700',
                            );
                          },
                        ),
                        SizedBox(
                          height: 3,
                        ),
                        NarFormLabelWidget(
                          label:
                              'Valido fino al: ${DateFormat('dd/MM/yyyy').format(expiryDate)}',
                          fontSize: 12,
                          fontWeight: '500',
                          textColor: isExpired
                              ? Color(0xffE82525)
                              : AppColor.black,
                        ),
                      ],
                    ),
                  ),
                  if (subscriptionType != "report") ...[
                  SizedBox(
                    height: 60,
                    child: Padding(
                      padding:
                          const EdgeInsets.symmetric(horizontal: 8.0),
                      child: VerticalDivider(
                        color: Color(0xffC3C3C3),
                      ),
                    ),
                  ),

                  Expanded(
                    flex: 1,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label: 'Crediti',
                          fontSize: 12,
                          fontWeight: '600',
                          textColor: isServiceOver
                              ? Color(0xffE82525)
                              : Color(0xff828282),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        NarFormLabelWidget(
                          label:
                              '${subscriptionServiceCountLeft}/${subscriptionServiceCount}',
                          fontSize: 16,
                          fontWeight: '700',
                          textColor: isServiceOver
                              ? Color(0xffE82525)
                              : AppColor.black,
                        ),
                      ],
                    ),
                  ),
                  ]
                ],
                ]
              ),
            ),
            SizedBox(
              height: 15,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 15.0),
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: 
                      !hasHadSubscription 
                      ? () {
                        scrollToSection(planKey);
                      }
                      : () {
                        // scrollToSection(_planKey);
                        String url = appConfig.isProduction
                          ? "https://billing.stripe.com/p/login/5kAg1T0ekex60iAeUU"
                          : "https://billing.stripe.com/p/login/test_28E14m3Xtd9FasS5ktfjG00";
                        // redirect to url in new tab
                        html.window.open(url, "");
                      },
                      child: Container(
                        height: 35,
                        width: 180,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Color(0xFFEBEBEB),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10.0),
                          child: NarFormLabelWidget(
                            label: !hasHadSubscription ? "Acquista" : 'Gestisci abbonamento',
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: AppColor.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
      ),
    );
  }

  StreamBuilder _yourSubscription() {
    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream: FirebaseFirestore.instance
          .collection(appConfig.COLLECT_AGENCIES)
          .doc(widget.agencyUser?.id)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox();
        }

        if (snapshot.hasError) {
          return SizedBox();
        }

        if (!snapshot.hasData || !snapshot.data!.exists) {
          return SizedBox();
        }
        // fetch agency data
        final agencyData = snapshot.data!.data();
        // get subscirptions data
        final subscriptionServiceCountLeft =
            agencyData?['subscriptionServiceCountLeft'] ?? 0;
        final subscriptionServiceCount =
            agencyData?['subscriptionServiceCount'] ?? 0;
        final int immaginaSmartServiceCountLeft = agencyData?['smartSubscriptionServiceCountLeft'] ?? 0;
        final int immaginaSmartServiceCount = agencyData?['smartSubscriptionServiceCount'] ?? 0;
        // get expiry dates
        DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(agencyData?['subscriptionEndDate'] ?? 0);
        DateTime reportExpiryDate = DateTime.fromMillisecondsSinceEpoch(agencyData?['reportSubscriptionEndDate'] ?? 0);
        DateTime immaginaSmartExpiryDate = DateTime.fromMillisecondsSinceEpoch(agencyData?['smartSubscriptionEndDate'] ?? 0);        
        // get subscriptions expired or credits finished
        bool hasHadSubscription = agencyData?['subscriptionId'] != null;
        bool hasHadReportSubscription = agencyData?['reportSubscriptionId'] != null;
        bool hasHadImmaginaSmartSubscription = agencyData?['smartSubscriptionId'] != null;

        return IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ///----- immagina pro
              _subscriptionStatusContainer("pro", agencyData?['subscriptionId'] ?? "", hasHadSubscription, expiryDate, subscriptionServiceCount, subscriptionServiceCountLeft, _planKey),
              SizedBox(width: 22,),
              ///----- immagina smart
              _subscriptionStatusContainer("smart", agencyData?['smartSubscriptionId'] ?? "", hasHadImmaginaSmartSubscription, immaginaSmartExpiryDate, immaginaSmartServiceCount, immaginaSmartServiceCountLeft, _smartPlanKey),
              SizedBox(width: 22,),
              ///----- immagina reports
              _subscriptionStatusContainer("report", agencyData?['reportSubscriptionId'] ?? "", hasHadReportSubscription, reportExpiryDate, 0, 0, _reportPlanKey),
            ],
          ),
        );
      },
    );
  }

}

class PricingCard extends StatelessWidget {
  final String title;
  final List<Map<String, String>> prices;
  final List<Map<String, String>> extraPrices;

  const PricingCard({
    required this.title,
    required this.prices,
    required this.extraPrices,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xffCFCFCF)),
        borderRadius: BorderRadius.circular(15)
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NarFormLabelWidget(
              label: title,
              fontSize: 19,
              fontWeight: '700',
              textColor: Colors.black,
            ),
            SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildPriceList(prices),
                Container(height: 90,color: Color(0xFFDBDBDB),width: 1,),
                _buildPriceList(extraPrices),
              ],
            ),
            SizedBox(height: 10),
            Center(
              child: NarFormLabelWidget(
                label: "Immobili oltre i 299mq - preventivo personalizzato",
                fontSize: 13,
                fontWeight: '500',
                textColor: Color(0xFF616161),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceList(List<Map<String, String>> priceList) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: priceList.map((item) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NarFormLabelWidget(
                label: "${item['label'] ?? ''} - ",
                fontSize: 13,
                fontWeight: '700',
                textColor: AppColor.black,
              ),
              NarFormLabelWidget(
                label: item['price'] ?? '',
                fontSize: 13,
                fontWeight: '500',
                textColor: AppColor.black,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

class SubscriptionCard extends StatefulWidget {
  final int selectedTab;
  final bool isSelected;
  final VoidCallback onTap;
  final Map<String, dynamic> subscription;
  final Agency? agencyUser;

  const SubscriptionCard({
    Key? key,
    required this.isSelected,
    required this.onTap,
    required this.subscription,
    required this.selectedTab,
    this.agencyUser,
  }) : super(key: key);

  @override
  _SubscriptionCardState createState() => _SubscriptionCardState();
}

class _SubscriptionCardState extends State<SubscriptionCard> {
  bool isHovered = false;
  final NumberFormat formatCurrency = NumberFormat.currency(
    locale: 'de_DE',
    symbol: '€',
    decimalDigits: 2,
  );

  @override
  Widget build(BuildContext context) {
    List<dynamic> services = widget.subscription['services'];
    final double price =
        double.tryParse(widget.subscription['price'].toString()) ?? 0.0;
    final double serviceCount =
        double.tryParse(widget.subscription['serviceCount'].toString()) ?? 1.0;
    final double successFee =
        double.tryParse(widget.subscription['successFee'].toString()) ?? 0.0;

    final double pricePerService = price / serviceCount;

    final String formattedPricePerService =
        formatCurrency.format(pricePerService);
    final String successFeeLabel = '$successFee% success fee';

    bool hasActiveSubscription =
        widget.subscription['id'] == widget.agencyUser?.subscriptionId;
    bool canPurchase = false;

    if (hasActiveSubscription &&
        (widget.agencyUser?.subscriptionServiceCountLeft ?? 0) <= 0) {
      canPurchase = true;
    } else if (widget.agencyUser?.subscriptionEndDate == null ||
        DateTime.parse(DateTime.fromMillisecondsSinceEpoch(
                    widget.agencyUser!.subscriptionEndDate ?? 0)
                .toString())
            .isBefore(DateTime.now())) {
      canPurchase = true;
    } else if (hasActiveSubscription &&
        (widget.agencyUser?.subscriptionServiceCountLeft ?? 0) > 0) {
      canPurchase = false;
    } else {
      canPurchase = true;
    }

    if (widget.subscription['id'] == 'custom') {
      canPurchase = false;
    }

    // return IgnorePointer(
    //   ignoring: !canPurchase,
    //   child: Opacity(
    return Opacity(
        opacity: 1,
        child: MouseRegion(
          onEnter: widget.subscription['id'] == 'custom' ? (_) {}
          : (_) {
            setState(() {
              isHovered = true;
            });
          },
          onExit: 
          widget.subscription['id'] == 'custom' ? (_) {}
          : (_) {
            setState(() {
              isHovered = false;
            });
          },
          child: InkWell(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            splashFactory: NoSplash.splashFactory,
            hoverColor: Colors.transparent,
            onTap: canPurchase ? widget.onTap : () {},
            mouseCursor: canPurchase ? SystemMouseCursors.click : SystemMouseCursors.basic,
            child: Align(
              alignment: Alignment.center,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 8),
                width: isHovered ? 260 : 250,
                height: isHovered ? 405 : 395,
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(18),
                  border: Border.all(
                    color: widget.isSelected
                        ? Theme.of(context).primaryColor
                        : Color(0xffCFCFCF),
                    width: widget.isSelected ? 3 : 1,
                  ),
                  boxShadow: isHovered
                      ? [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 15,
                            offset: const Offset(0, 15),
                          )
                        ]
                      : [],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    NarFormLabelWidget(
                      label: widget.subscription['planType'] ?? "",
                      fontSize: 25,
                      fontWeight: '700',
                    ),
                    NarFormLabelWidget(
                      label: widget.subscription['servicePerYear'] ?? "",
                      fontSize: 14,
                      fontWeight: '800',
                      textColor: AppColor.black,
                    ),
                    SizedBox(height: 5,),
                    NarFormLabelWidget(
                      label: "Case fino a ${gSFUpperLimit - 1}mq",
                      fontSize: 11,
                      fontWeight: '600',
                      textColor: AppColor.black,
                    ),
                    SizedBox(
                      height: 17,
                    ),
                    ListView.separated(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      physics: ScrollPhysics(),
                      itemCount: services.length,
                      itemBuilder: (context, i) {
                        return Row(
                          children: [
                            SvgPicture.asset(
                              "assets/icons/true.svg",
                              height: 10,
                              color: const Color(0xffA4A4A4),
                            ),
                            SizedBox(
                              width: 8,
                            ),
                            Container(
                              width: 180,
                              child: NarFormLabelWidget(
                                label: services[i],
                                fontSize: 12,
                                fontWeight: '500',
                                textColor: Color(0xff575757),
                                overflow: TextOverflow.clip,
                              ),
                            ),
                          ],
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return SizedBox(
                          height: 8,
                        );
                      },
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    Spacer(),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        NarFormLabelWidget(
                          label:
                            widget.subscription['price'] == 0 ? "Contattaci!" :
                              formatCurrency.format(widget.subscription['price']),
                          fontSize: 20,
                          fontWeight: '700',
                          textColor: AppColor.black,
                        ),
                        NarFormLabelWidget(
                          label:
                            widget.subscription['price'] == 0 ? "" :
                              " +iva/" + (widget.subscription['monthsBillingFrequency'] == 12 ? "anno" : widget.subscription['monthsBillingFrequency'] == 2 ? "2 mesi" : "mese"),
                          fontSize: 15,
                          fontWeight: '700',
                          textColor: AppColor.black,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 2,
                    ),
                    if (widget.selectedTab == 0 &&  widget.subscription['price'] != 0) ...[
                      NarFormLabelWidget(
                        label:
                            '+ ${widget.subscription['successFee']}% success fee',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: AppColor.black,
                      ),
                    ] else if (widget.subscription['price'] == 0) ...[
                      NarFormLabelWidget(
                        label: '<EMAIL>',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: AppColor.black,
                      ),
                    ] else ...[
                      NarFormLabelWidget(
                        label: '',
                        fontSize: 12,
                        fontWeight: '500',
                        textColor: AppColor.black,
                      ),
                    ],
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Divider(
                        height: 1,
                        color: widget.subscription['price'] != 0 ? Color(0xffDBDBDB) : Colors.transparent,
                      ),
                    ),
                      if (widget.subscription['price'] == 0) ...[
                        NarFormLabelWidget(
                          label: '',
                          fontSize: 12,
                          fontWeight: '600',
                          textColor: Color(0xff9A9A9A),
                        ),
                      ] else if (widget.selectedTab == 0) ...[
                        NarFormLabelWidget(
                          label: '$formattedPricePerService/servizio + $successFeeLabel',
                          fontSize: 12,
                          fontWeight: '600',
                          textColor: Color(0xff9A9A9A),
                        ),
                      ] else ...[
                        NarFormLabelWidget(
                          label: '${formatCurrency.format(widget.subscription['price'] / widget.subscription['serviceCount'])}/servizio',
                          fontSize: 12,
                          fontWeight: '600',
                          textColor: Color(0xff9A9A9A),
                        ),
                      ],



                  ],
                ),
              ),
            ),
          ),
        ),
      // ),
    );
  }
}

class ReportCard extends StatefulWidget {
  final bool isSelected;
  final Map<String, dynamic> subscription;
  final VoidCallback onTap;
  final Agency? agencyUser;

  const ReportCard({
    Key? key,
    required this.isSelected,
    required this.subscription,
    required this.onTap,
    this.agencyUser,
  }) : super(key: key);

  @override
  State<ReportCard> createState() => _ReportCardState();
}

class _ReportCardState extends State<ReportCard> {
  bool isHoveredReportCard = false;

  @override
  Widget build(BuildContext context) {
    final formatCurrency = NumberFormat.currency(locale: 'de_DE', symbol: '€', decimalDigits: 2);
    final price = double.tryParse(widget.subscription['price'].toString()) ?? 0.0;
    final tag = widget.subscription['tag'];
    final billingFreq = widget.subscription['monthsBillingFrequency'] as int;
    final term = billingFreq == 12
        ? 'anno'
        : billingFreq == 1
        ? 'mese'
        : '$billingFreq mesi';
    final priceLabel =
    '${formatCurrency.format(price)}';

    return MouseRegion(
      onEnter: (_) => setState(() => isHoveredReportCard = true),
      onExit: (_) => setState(() => isHoveredReportCard = false),
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Stack(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.all(20),
              width: isHoveredReportCard ? 281 : 271,
              height: 370,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(18),
                border: Border.all(
                  color: widget.isSelected
                      ? Theme.of(context).primaryColor
                      : const Color(0xffCFCFCF),
                  width: widget.isSelected ? 3 : 1,
                ),
                boxShadow: isHoveredReportCard
                    ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 15),
                  )
                ]
                    : [],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NarFormLabelWidget(
                    label: "Newarc Reports",
                    fontSize: 25,
                    fontWeight: '700',
                    textColor: AppColor.black,
                  ),
                  const SizedBox(height: 5),
                  NarFormLabelWidget(
                    label: widget.subscription['planType'] ?? '',
                    fontSize: 15,
                    fontWeight: '800',
                    textColor: AppColor.black,
                  ),
                  // const SizedBox(height: 18),
                  const Spacer(),
                  ...List.generate(
                    (widget.subscription['services'] as List).length,
                        (i) => Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        children: [
                          SvgPicture.asset(
                            "assets/icons/true.svg",
                            height: 10,
                            color: const Color(0xffA4A4A4),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: NarFormLabelWidget(
                              label: widget.subscription['services'][i],
                              fontSize: 12,
                              fontWeight: '500',
                              textColor: const Color(0xff575757),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const Spacer(),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      NarFormLabelWidget(
                        label: priceLabel,
                        fontSize: 20,
                        fontWeight: '700',
                        textColor: AppColor.black,
                      ),
                      NarFormLabelWidget(
                        label: "+iva/$term",
                        fontSize: 15,
                        fontWeight: '700',
                        textColor: AppColor.black,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Positioned green badge
            if(tag != null)
            Positioned(
              top: 0,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 5,vertical: 2),
                decoration: BoxDecoration(
                  color: Color(0xFF39C14F),
                  borderRadius: BorderRadius.circular(20),
                ),
                alignment: Alignment.center,
                child:NarFormLabelWidget(
                  label: tag.toString(),
                  fontSize: 10,
                  fontWeight: '600',
                  textColor: AppColor.white,
                  letterSpacing: .5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}



class PaymentTabToggle extends StatelessWidget {
  final bool isMonthly;
  final Function(bool) onChanged;

  const PaymentTabToggle({
    super.key,
    required this.isMonthly,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: Stack(
        children: [
          Container(
            height: 55,
            width: 400,
            padding: EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: const Color(0xFFF2F2F2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Row(
              children: [
                // Monthly Button
                Expanded(
                  child: GestureDetector(
                    onTap: () => onChanged(true),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isMonthly ? Colors.white : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        // border: isMonthly ? Border.all(color: Color(0xFFDFDFDF), width: 1) : null,
                      ),
                      alignment: Alignment.center,
                      child:NarFormLabelWidget(
                        label: "Pagamento mensile",
                        fontSize: 13,
                        fontWeight: '700',
                        textColor: isMonthly ? AppColor.black : Color(0xFF7C7C7C),
                      ),
                    ),
                  ),
                ),

                // Annual Button
                Expanded(
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      GestureDetector(
                        onTap: () => onChanged(false),
                        child: Container(
                          decoration: BoxDecoration(
                            color: !isMonthly ? Colors.white : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                            // border: !isMonthly ? Border.all(color: Color(0xFFDFDFDF), width: 1) : null,
                          ),
                          alignment: Alignment.center,
                          child:NarFormLabelWidget(
                            label: "Pagamento annuale",
                            fontSize: 13,
                            fontWeight: '700',
                            textColor: !isMonthly ? AppColor.black : Color(0xFF7C7C7C),
                          ),
                        ),
                      ),
                      Positioned(
                        top: -12,
                        left: 0,
                        right: 0,
                        child: Align(
                          alignment: Alignment.center,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              color: Color(0xFF39C14F),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            height: 15,
                            child:NarFormLabelWidget(
                              label: "Il più vantaggioso",
                              fontSize: 10,
                              fontWeight: '600',
                              textColor:  AppColor.white ,
                              letterSpacing: .5,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}



