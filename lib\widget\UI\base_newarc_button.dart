import 'package:flutter/material.dart';
import 'dart:math';
import 'package:newarc_platform/widget/UI/form-label.dart';

class BaseNewarcButton extends StatefulWidget {
  final String? buttonText;
  final Function onPressed;
  final bool? notAccent;
  final Color? textColor;
  final Color? color;
  final Color? borderColor;
  final String? fontWeight;
  double? height = 35;
  double? width;
  double? fontSize = 15;
  double? horizontalPadding = 30;
  double? borderRadius = 8;
  bool? disableButton;
  Widget? suffixIcon;
  Widget? prefixIcon;

  BaseNewarcButton({
    required this.onPressed,
    this.notAccent = false,
    this.buttonText,
    this.height = 35,
    this.width,
    this.fontSize = 15,
    this.horizontalPadding = 10,
    this.borderRadius = 8,
    this.disableButton = false,
    this.textColor = Colors.white,
    this.fontWeight = '600',
    this.color,
    this.borderColor = Colors.transparent,
    this.suffixIcon,
    this.prefixIcon,
    Key? key,
  }) : super(key: key);

  @override
  State<BaseNewarcButton> createState() => _BaseNewarcButtonState();
}

class _BaseNewarcButtonState extends State<BaseNewarcButton> {
  // bool notAccent = false;
  bool disableButton = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(BaseNewarcButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    disableButton = widget.disableButton!; 
    // notAccent = widget.notAccent!;
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: disableButton ? SystemMouseCursors.basic : SystemMouseCursors.click,
      child: GestureDetector(
        onTap: disableButton
        ? null
        : () async {
          await widget.onPressed();
        },
        child: Container(
          height: widget.height,
          width: widget.width != null 
              ? (widget.width! > 0 ? max(widget.width!, 100.0) : null) 
              : null,
          constraints: BoxConstraints(
            minWidth: 100.0,
          ),
          decoration: BoxDecoration(
            color: widget.color == null ? (widget.notAccent! ? Color(0xffcccccc) : Theme.of(context).primaryColor) : widget.color,
            // borderRadius: BorderRadius.circular(7),
            border: Border.all(
                width: 1, 
                color: widget.borderColor!
                // color: Colors.transparent
              ),
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 8),
          ),
          child: Padding(
            padding:
                EdgeInsets.symmetric(horizontal: widget.horizontalPadding!),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                widget.prefixIcon ?? SizedBox.shrink(),
                NarFormLabelWidget(
                  label: widget.buttonText == null ? 'OK' : widget.buttonText,
                  textColor: widget.textColor,
                  fontSize: widget.fontSize,
                  fontWeight: widget.fontWeight,
                ),
                widget.suffixIcon ?? SizedBox.shrink(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
